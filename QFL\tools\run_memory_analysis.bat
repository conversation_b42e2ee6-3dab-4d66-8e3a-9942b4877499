@echo off
chcp 65001 >nul
title 起凡战功内存分析工具 By @ConceptualGod

echo ================================================
echo 起凡战功内存分析工具 By @ConceptualGod
echo ================================================
echo.

:MENU
echo 请选择要运行的工具:
echo.
echo 1. 基础内存分析器 (memory_analyzer.py)
echo 2. 战功内存扫描器 (zhangong_memory_scanner.py)
echo 3. 实时内存监控器 (memory_monitor.py)
echo 4. 运行所有工具 (依次执行)
echo 0. 退出
echo.

set /p choice="请输入选择 (0-4): "

if "%choice%"=="0" goto EXIT
if "%choice%"=="1" goto RUN_ANALYZER
if "%choice%"=="2" goto RUN_SCANNER
if "%choice%"=="3" goto RUN_MONITOR
if "%choice%"=="4" goto RUN_ALL

echo 无效选择，请重新输入
echo.
goto MENU

:RUN_ANALYZER
echo.
echo 启动基础内存分析器...
echo ================================================
python memory_analyzer.py
echo.
echo 分析完成，按任意键返回菜单...
pause >nul
goto MENU

:RUN_SCANNER
echo.
echo 启动战功内存扫描器...
echo ================================================
python zhangong_memory_scanner.py
echo.
echo 扫描完成，按任意键返回菜单...
pause >nul
goto MENU

:RUN_MONITOR
echo.
echo 启动实时内存监控器...
echo ================================================
python memory_monitor.py
echo.
echo 监控结束，按任意键返回菜单...
pause >nul
goto MENU

:RUN_ALL
echo.
echo 依次运行所有分析工具...
echo ================================================
echo.

echo [1/3] 运行基础内存分析器...
python memory_analyzer.py
echo.
echo 基础分析完成，按任意键继续...
pause >nul

echo.
echo [2/3] 运行战功内存扫描器...
python zhangong_memory_scanner.py
echo.
echo 扫描完成，按任意键继续...
pause >nul

echo.
echo [3/3] 运行实时内存监控器...
echo 注意: 这是交互式工具，请按照提示操作
python memory_monitor.py
echo.
echo 所有工具运行完成，按任意键返回菜单...
pause >nul
goto MENU

:EXIT
echo.
echo 感谢使用起凡战功内存分析工具 By @ConceptualGod
echo.
pause
