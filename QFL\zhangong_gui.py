#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
起凡战功识别GUI界面

通过内存读取实时识别游戏内战功任务的图形界面
开发者: @ConceptualGod
创建时间: 2025-07-28
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import logging
from datetime import datetime
from typing import List, Dict, Any
from core.memory_reader import GameMemoryReader, ZhanGongTask


class ZhanGongGUI:
    """
    战功识别GUI界面
    
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化GUI界面
        
        开发者: @ConceptualGod
        """
        self.root = tk.Tk()
        self.root.title("起凡战功识别系统 By @ConceptualGod")
        self.root.geometry("900x700")
        self.root.resizable(True, True)
        
        # 初始化组件
        self.memory_reader = GameMemoryReader()
        self.is_monitoring = False
        self.monitor_thread = None
        self.last_tasks = []
        
        # 设置日志
        self.setup_logging()
        
        # 创建界面
        self.create_widgets()
        
        # 启动时检查游戏状态
        self.root.after(1000, self.check_initial_status)
    
    def setup_logging(self):
        """
        设置日志配置
        
        开发者: @ConceptualGod
        """
        # 创建自定义日志处理器，将日志输出到GUI
        self.log_handler = GUILogHandler(self)
        self.log_handler.setLevel(logging.INFO)
        
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
        self.log_handler.setFormatter(formatter)
        
        # 获取根日志器并添加处理器
        logger = logging.getLogger()
        logger.addHandler(self.log_handler)
        logger.setLevel(logging.INFO)
    
    def create_widgets(self):
        """
        创建界面组件
        
        开发者: @ConceptualGod
        """
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="起凡战功识别系统", font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=2, pady=(0, 10))
        
        # 控制面板
        self.create_control_panel(main_frame)
        
        # 任务显示区域
        self.create_task_display(main_frame)
        
        # 日志显示区域
        self.create_log_display(main_frame)
        
        # 状态栏
        self.create_status_bar()
    
    def create_control_panel(self, parent):
        """
        创建控制面板
        
        Args:
            parent: 父容器
            
        开发者: @ConceptualGod
        """
        control_frame = ttk.LabelFrame(parent, text="控制面板", padding="10")
        control_frame.grid(row=1, column=0, sticky=(tk.W, tk.E, tk.N), padx=(0, 5))
        
        # 游戏状态
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill=tk.X, pady=(0, 10))
        
        ttk.Label(status_frame, text="游戏状态:").pack(side=tk.LEFT)
        self.game_status_label = ttk.Label(status_frame, text="检查中...", foreground="orange")
        self.game_status_label.pack(side=tk.LEFT, padx=(5, 0))
        
        # 按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=5)
        
        self.check_button = ttk.Button(button_frame, text="检查游戏", command=self.check_game_status)
        self.check_button.pack(fill=tk.X, pady=2)
        
        self.read_button = ttk.Button(button_frame, text="读取任务", command=self.read_tasks)
        self.read_button.pack(fill=tk.X, pady=2)
        
        self.monitor_button = ttk.Button(button_frame, text="开始监控", command=self.toggle_monitoring)
        self.monitor_button.pack(fill=tk.X, pady=2)
        
        self.clear_button = ttk.Button(button_frame, text="清空日志", command=self.clear_log)
        self.clear_button.pack(fill=tk.X, pady=2)
        
        # 统计信息
        stats_frame = ttk.LabelFrame(control_frame, text="统计信息", padding="5")
        stats_frame.pack(fill=tk.X, pady=(10, 0))
        
        self.total_tasks_label = ttk.Label(stats_frame, text="总任务: 0")
        self.total_tasks_label.pack(anchor=tk.W)
        
        self.completed_tasks_label = ttk.Label(stats_frame, text="已完成: 0")
        self.completed_tasks_label.pack(anchor=tk.W)
        
        self.progress_tasks_label = ttk.Label(stats_frame, text="进行中: 0")
        self.progress_tasks_label.pack(anchor=tk.W)
    
    def create_task_display(self, parent):
        """
        创建任务显示区域
        
        Args:
            parent: 父容器
            
        开发者: @ConceptualGod
        """
        task_frame = ttk.LabelFrame(parent, text="战功任务列表", padding="10")
        task_frame.grid(row=1, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(5, 0))
        task_frame.columnconfigure(0, weight=1)
        task_frame.rowconfigure(0, weight=1)
        
        # 创建Treeview
        columns = ("ID", "类型", "进度", "状态", "描述")
        self.task_tree = ttk.Treeview(task_frame, columns=columns, show="headings", height=15)
        
        # 设置列标题和宽度
        self.task_tree.heading("ID", text="ID")
        self.task_tree.heading("类型", text="任务类型")
        self.task_tree.heading("进度", text="进度")
        self.task_tree.heading("状态", text="状态")
        self.task_tree.heading("描述", text="任务描述")
        
        self.task_tree.column("ID", width=50, anchor=tk.CENTER)
        self.task_tree.column("类型", width=100, anchor=tk.CENTER)
        self.task_tree.column("进度", width=80, anchor=tk.CENTER)
        self.task_tree.column("状态", width=80, anchor=tk.CENTER)
        self.task_tree.column("描述", width=300, anchor=tk.W)
        
        # 添加滚动条
        task_scrollbar = ttk.Scrollbar(task_frame, orient=tk.VERTICAL, command=self.task_tree.yview)
        self.task_tree.configure(yscrollcommand=task_scrollbar.set)
        
        # 布局
        self.task_tree.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        task_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
    
    def create_log_display(self, parent):
        """
        创建日志显示区域
        
        Args:
            parent: 父容器
            
        开发者: @ConceptualGod
        """
        log_frame = ttk.LabelFrame(parent, text="系统日志", padding="10")
        log_frame.grid(row=2, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 5), pady=(10, 0))
        log_frame.columnconfigure(0, weight=1)
        log_frame.rowconfigure(0, weight=1)
        
        # 创建文本框
        self.log_text = scrolledtext.ScrolledText(log_frame, height=10, wrap=tk.WORD)
        self.log_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 添加初始日志
        self.add_log("系统启动完成 - By @ConceptualGod")
    
    def create_status_bar(self):
        """
        创建状态栏
        
        开发者: @ConceptualGod
        """
        status_frame = ttk.Frame(self.root)
        status_frame.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=10, pady=(0, 10))
        status_frame.columnconfigure(0, weight=1)
        
        # 状态信息
        self.status_label = ttk.Label(status_frame, text="就绪")
        self.status_label.grid(row=0, column=0, sticky=tk.W)
        
        # 署名水印
        signature_label = ttk.Label(status_frame, text="By @ConceptualGod", 
                                   foreground="gray", font=("Arial", 8))
        signature_label.grid(row=0, column=1, sticky=tk.E)
    
    def check_initial_status(self):
        """
        检查初始游戏状态
        
        开发者: @ConceptualGod
        """
        self.check_game_status()
    
    def check_game_status(self):
        """
        检查游戏状态
        
        开发者: @ConceptualGod
        """
        def check_in_thread():
            try:
                if self.memory_reader.find_game_process():
                    self.root.after(0, lambda: self.update_game_status("运行中", "green"))
                    self.add_log("游戏进程检测成功 - By @ConceptualGod")
                else:
                    self.root.after(0, lambda: self.update_game_status("未运行", "red"))
                    self.add_log("未检测到游戏进程 - By @ConceptualGod")
            except Exception as e:
                self.root.after(0, lambda: self.update_game_status("检查失败", "red"))
                self.add_log(f"游戏状态检查异常 - By @ConceptualGod: {str(e)}")
        
        threading.Thread(target=check_in_thread, daemon=True).start()
    
    def update_game_status(self, status: str, color: str):
        """
        更新游戏状态显示
        
        Args:
            status: 状态文本
            color: 状态颜色
            
        开发者: @ConceptualGod
        """
        self.game_status_label.config(text=status, foreground=color)
    
    def read_tasks(self):
        """
        读取战功任务
        
        开发者: @ConceptualGod
        """
        def read_in_thread():
            try:
                self.root.after(0, lambda: self.status_label.config(text="正在读取任务..."))
                
                tasks = self.memory_reader.read_zhangong_tasks()
                
                if tasks:
                    self.last_tasks = tasks
                    self.root.after(0, lambda: self.update_task_display(tasks))
                    self.add_log(f"成功读取 {len(tasks)} 个战功任务 - By @ConceptualGod")
                else:
                    self.add_log("未能读取到战功任务数据 - By @ConceptualGod")
                    messagebox.showwarning("警告 - By @ConceptualGod", 
                                         "未能读取到战功任务数据\n请确保游戏正在运行且已进入主界面")
                
                self.root.after(0, lambda: self.status_label.config(text="就绪"))
                
            except Exception as e:
                self.add_log(f"读取任务异常 - By @ConceptualGod: {str(e)}")
                self.root.after(0, lambda: messagebox.showerror("错误 - By @ConceptualGod", 
                                                               f"读取任务失败: {str(e)}"))
                self.root.after(0, lambda: self.status_label.config(text="就绪"))
        
        threading.Thread(target=read_in_thread, daemon=True).start()
    
    def update_task_display(self, tasks: List[ZhanGongTask]):
        """
        更新任务显示
        
        Args:
            tasks: 任务列表
            
        开发者: @ConceptualGod
        """
        # 清空现有数据
        for item in self.task_tree.get_children():
            self.task_tree.delete(item)
        
        # 添加新数据
        for task in tasks:
            progress_str = f"{task.current_progress}/{task.target_progress}"
            status_str = "已完成" if task.is_completed else "进行中"
            
            # 根据状态设置不同的标签
            tag = "completed" if task.is_completed else "progress"
            
            self.task_tree.insert("", tk.END, values=(
                task.task_id,
                task.task_type,
                progress_str,
                status_str,
                task.task_desc
            ), tags=(tag,))
        
        # 设置标签样式
        self.task_tree.tag_configure("completed", background="#e8f5e8")
        self.task_tree.tag_configure("progress", background="#fff8dc")
        
        # 更新统计信息
        self.update_statistics(tasks)
    
    def update_statistics(self, tasks: List[ZhanGongTask]):
        """
        更新统计信息
        
        Args:
            tasks: 任务列表
            
        开发者: @ConceptualGod
        """
        total_count = len(tasks)
        completed_count = sum(1 for task in tasks if task.is_completed)
        progress_count = total_count - completed_count
        
        self.total_tasks_label.config(text=f"总任务: {total_count}")
        self.completed_tasks_label.config(text=f"已完成: {completed_count}")
        self.progress_tasks_label.config(text=f"进行中: {progress_count}")
    
    def toggle_monitoring(self):
        """
        切换监控状态
        
        开发者: @ConceptualGod
        """
        if self.is_monitoring:
            self.stop_monitoring()
        else:
            self.start_monitoring()
    
    def start_monitoring(self):
        """
        开始监控
        
        开发者: @ConceptualGod
        """
        if self.is_monitoring:
            return
        
        self.is_monitoring = True
        self.monitor_button.config(text="停止监控")
        self.status_label.config(text="监控中...")
        
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.add_log("开始实时监控 - By @ConceptualGod")
    
    def stop_monitoring(self):
        """
        停止监控
        
        开发者: @ConceptualGod
        """
        if not self.is_monitoring:
            return
        
        self.is_monitoring = False
        self.monitor_button.config(text="开始监控")
        self.status_label.config(text="就绪")
        
        self.add_log("停止实时监控 - By @ConceptualGod")
    
    def monitor_loop(self):
        """
        监控循环
        
        开发者: @ConceptualGod
        """
        try:
            while self.is_monitoring:
                try:
                    tasks = self.memory_reader.read_zhangong_tasks()
                    
                    if tasks:
                        # 检查变化
                        changes = self.detect_task_changes(self.last_tasks, tasks)
                        
                        if changes:
                            for change in changes:
                                self.add_log(f"任务变化: {change}")
                        
                        # 更新显示
                        self.last_tasks = tasks
                        self.root.after(0, lambda t=tasks: self.update_task_display(t))
                    
                except Exception as e:
                    self.add_log(f"监控异常 - By @ConceptualGod: {str(e)}")
                
                # 等待5秒
                for _ in range(50):
                    if not self.is_monitoring:
                        break
                    time.sleep(0.1)
                    
        except Exception as e:
            self.add_log(f"监控循环异常 - By @ConceptualGod: {str(e)}")
    
    def detect_task_changes(self, old_tasks: List[ZhanGongTask], new_tasks: List[ZhanGongTask]) -> List[str]:
        """
        检测任务变化
        
        Args:
            old_tasks: 旧任务列表
            new_tasks: 新任务列表
            
        Returns:
            变化描述列表
            
        开发者: @ConceptualGod
        """
        changes = []
        
        if not old_tasks:
            return changes
        
        old_dict = {task.task_id: task for task in old_tasks}
        new_dict = {task.task_id: task for task in new_tasks}
        
        for task_id, new_task in new_dict.items():
            if task_id in old_dict:
                old_task = old_dict[task_id]
                
                if new_task.current_progress != old_task.current_progress:
                    changes.append(f"{new_task.task_desc}: 进度 {old_task.current_progress} -> {new_task.current_progress}")
                
                if new_task.is_completed != old_task.is_completed:
                    status = "完成" if new_task.is_completed else "重置"
                    changes.append(f"{new_task.task_desc}: {status}")
        
        return changes
    
    def add_log(self, message: str):
        """
        添加日志
        
        Args:
            message: 日志消息
            
        开发者: @ConceptualGod
        """
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        def update_log():
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)
        
        if threading.current_thread() == threading.main_thread():
            update_log()
        else:
            self.root.after(0, update_log)
    
    def clear_log(self):
        """
        清空日志
        
        开发者: @ConceptualGod
        """
        self.log_text.delete(1.0, tk.END)
        self.add_log("日志已清空 - By @ConceptualGod")
    
    def run(self):
        """
        运行GUI
        
        开发者: @ConceptualGod
        """
        try:
            self.root.mainloop()
        finally:
            # 清理资源
            if self.is_monitoring:
                self.stop_monitoring()
            self.memory_reader.close_process_handle()


class GUILogHandler(logging.Handler):
    """
    GUI日志处理器
    
    开发者: @ConceptualGod
    """
    
    def __init__(self, gui_instance):
        """
        初始化日志处理器
        
        Args:
            gui_instance: GUI实例
            
        开发者: @ConceptualGod
        """
        super().__init__()
        self.gui = gui_instance
    
    def emit(self, record):
        """
        发送日志记录
        
        Args:
            record: 日志记录
            
        开发者: @ConceptualGod
        """
        try:
            msg = self.format(record)
            self.gui.add_log(msg)
        except Exception:
            pass


def main():
    """
    主函数
    
    开发者: @ConceptualGod
    """
    try:
        app = ZhanGongGUI()
        app.run()
    except Exception as e:
        messagebox.showerror("错误 - By @ConceptualGod", f"程序启动失败: {str(e)}")


if __name__ == "__main__":
    main()
