#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
起凡游戏状态读取模块

实现游戏运行状态的实时监控和日志分析
开发者: @ConceptualGod
"""

import os
import re
import time
import psutil
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from datetime import datetime
from .game_data_reader import GameDataReader


class GameStateReader(GameDataReader):
    """游戏状态读取器"""
    
    def __init__(self, game_path: str):
        super().__init__(game_path)
        self.game_process_names = ['7FGame.exe', 'game.exe', '7fsanguo.exe']
        self.log_cache = {}
        self.last_log_check = 0
    
    def read_data(self) -> Dict[str, Any]:
        """读取游戏状态数据"""
        return {
            'process_status': self.check_game_process(),
            'current_game_state': self.read_current_game_state(),
            'latest_logs': self.read_latest_game_logs(),
            'game_result': self.get_latest_game_result()
        }
    
    def check_game_process(self) -> Dict[str, Any]:
        """
        检查游戏进程状态
        
        Returns:
            进程状态信息
        """
        process_info = {
            'platform_running': False,
            'game_running': False,
            'processes': []
        }
        
        try:
            for proc in psutil.process_iter(['pid', 'name', 'create_time']):
                proc_name = proc.info['name']
                
                if proc_name in self.game_process_names:
                    process_data = {
                        'name': proc_name,
                        'pid': proc.info['pid'],
                        'create_time': proc.info['create_time'],
                        'running_time': time.time() - proc.info['create_time']
                    }
                    process_info['processes'].append(process_data)
                    
                    if proc_name == '7FGame.exe':
                        process_info['platform_running'] = True
                    elif proc_name in ['game.exe', '7fsanguo.exe']:
                        process_info['game_running'] = True
            
            self.logger.debug(f"检测到 {len(process_info['processes'])} 个游戏相关进程")
            
        except Exception as e:
            self.logger.error(f"检查游戏进程失败: {e}")
        
        return process_info
    
    def read_current_game_state(self) -> Dict[str, Any]:
        """
        读取当前游戏状态
        
        Returns:
            游戏状态信息
        """
        state = {
            'in_game': False,
            'game_mode': 'unknown',
            'game_time': 0,
            'player_info': {},
            'hero_info': {}
        }
        
        # 查找最新的游戏日志目录
        latest_log_dir = self._find_latest_log_directory()
        if not latest_log_dir:
            return state
        
        # 读取游戏状态相关日志
        state['in_game'] = self._check_game_in_progress(latest_log_dir)
        state['player_info'] = self._read_player_info(latest_log_dir)
        state['hero_info'] = self._read_hero_info(latest_log_dir)
        
        return state
    
    def read_latest_game_logs(self) -> List[Dict[str, Any]]:
        """
        读取最新的游戏日志
        
        Returns:
            日志条目列表
        """
        logs = []
        latest_log_dir = self._find_latest_log_directory()
        
        if not latest_log_dir:
            return logs
        
        # 读取关键日志文件
        log_files = ['init.log', 'cmd.log', 'end.log', 'error.log']
        
        for log_file in log_files:
            log_path = latest_log_dir / log_file
            if log_path.exists():
                log_entries = self._parse_log_file(log_path)
                logs.extend(log_entries)
        
        # 按时间排序
        logs.sort(key=lambda x: x.get('timestamp', ''))
        
        return logs[-50:]  # 返回最新的50条日志
    
    def get_latest_game_result(self) -> Optional[Dict[str, Any]]:
        """
        获取最新游戏结果
        
        Returns:
            游戏结果信息
        """
        latest_log_dir = self._find_latest_log_directory()
        if not latest_log_dir:
            return None
        
        # 读取游戏结束日志
        end_log_path = latest_log_dir / 'end.log'
        if not end_log_path.exists():
            return None
        
        try:
            content = self._safe_read_file(end_log_path)
            if not content:
                return None
            
            # 解析游戏结果
            result = self._parse_game_result(content)
            return result
            
        except Exception as e:
            self.logger.error(f"读取游戏结果失败: {e}")
            return None
    
    def monitor_game_progress(self) -> Dict[str, Any]:
        """
        监控游戏进度
        
        Returns:
            游戏进度信息
        """
        progress = {
            'game_time': 0,
            'kills': 0,
            'assists': 0,
            'deaths': 0,
            'mvp_score': 0,
            'sacrifice_value': 0,
            'hero_damage': 0,
            'hero_heal': 0
        }
        
        latest_log_dir = self._find_latest_log_directory()
        if not latest_log_dir:
            return progress
        
        # 读取统计日志
        score_log_path = latest_log_dir / 'scoreLOG.log'
        if score_log_path.exists():
            progress.update(self._parse_score_log(score_log_path))
        
        return progress
    
    def _find_latest_log_directory(self) -> Optional[Path]:
        """
        查找最新的游戏日志目录
        
        Returns:
            最新日志目录路径
        """
        game_dir = self.game_path / 'game'
        if not game_dir.exists():
            return None
        
        log_dirs = []
        for item in game_dir.iterdir():
            if item.is_dir() and item.name.startswith('log'):
                log_dirs.append(item)
        
        if not log_dirs:
            return None
        
        # 按修改时间排序，返回最新的
        log_dirs.sort(key=lambda x: x.stat().st_mtime, reverse=True)
        return log_dirs[0]
    
    def _check_game_in_progress(self, log_dir: Path) -> bool:
        """
        检查游戏是否正在进行
        
        Args:
            log_dir: 日志目录
            
        Returns:
            是否在游戏中
        """
        # 检查是否有活跃的游戏日志文件
        active_files = ['cmd.log', 'hinfo.log', 'pinfo.log']
        
        for file_name in active_files:
            file_path = log_dir / file_name
            if file_path.exists():
                # 检查文件最近是否有更新（5分钟内）
                mtime = file_path.stat().st_mtime
                if time.time() - mtime < 300:  # 5分钟
                    return True
        
        return False
    
    def _read_player_info(self, log_dir: Path) -> Dict[str, Any]:
        """
        读取玩家信息
        
        Args:
            log_dir: 日志目录
            
        Returns:
            玩家信息
        """
        player_info = {}
        pinfo_path = log_dir / 'pinfo.log'
        
        if pinfo_path.exists():
            content = self._safe_read_file(pinfo_path)
            if content:
                # 解析玩家信息（需要根据实际日志格式调整）
                player_info = self._parse_player_info(content)
        
        return player_info
    
    def _read_hero_info(self, log_dir: Path) -> Dict[str, Any]:
        """
        读取英雄信息
        
        Args:
            log_dir: 日志目录
            
        Returns:
            英雄信息
        """
        hero_info = {}
        hinfo_path = log_dir / 'hinfo.log'
        
        if hinfo_path.exists():
            content = self._safe_read_file(hinfo_path)
            if content:
                # 解析英雄信息（需要根据实际日志格式调整）
                hero_info = self._parse_hero_info(content)
        
        return hero_info
    
    def _parse_log_file(self, log_path: Path) -> List[Dict[str, Any]]:
        """
        解析日志文件
        
        Args:
            log_path: 日志文件路径
            
        Returns:
            日志条目列表
        """
        logs = []
        content = self._safe_read_file(log_path)
        
        if not content:
            return logs
        
        lines = content.split('\n')
        for line in lines:
            if line.strip():
                log_entry = self._parse_log_line(line)
                if log_entry:
                    logs.append(log_entry)
        
        return logs
    
    def _parse_log_line(self, line: str) -> Optional[Dict[str, Any]]:
        """
        解析单行日志
        
        Args:
            line: 日志行
            
        Returns:
            日志条目字典
        """
        # 匹配时间戳格式 [25-07-24 05:27:52:646]
        timestamp_pattern = r'\[(\d{2}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}:\d{3})\]'
        match = re.match(timestamp_pattern, line)
        
        if match:
            timestamp = match.group(1)
            message = line[match.end():].strip()
            
            return {
                'timestamp': timestamp,
                'message': message,
                'level': self._determine_log_level(message)
            }
        
        return None
    
    def _determine_log_level(self, message: str) -> str:
        """
        确定日志级别
        
        Args:
            message: 日志消息
            
        Returns:
            日志级别
        """
        message_lower = message.lower()
        
        if 'error' in message_lower or '错误' in message_lower:
            return 'ERROR'
        elif 'warning' in message_lower or '警告' in message_lower:
            return 'WARNING'
        elif 'info' in message_lower or '信息' in message_lower:
            return 'INFO'
        else:
            return 'DEBUG'
    
    def _parse_game_result(self, content: str) -> Dict[str, Any]:
        """
        解析游戏结果
        
        Args:
            content: 日志内容
            
        Returns:
            游戏结果字典
        """
        result = {
            'victory': False,
            'escape': False,
            'mvp': False,
            'kills': 0,
            'assists': 0,
            'deaths': 0
        }
        
        # 根据实际日志格式解析结果
        # 这里需要根据实际的end.log格式来实现
        
        return result
    
    def _parse_score_log(self, score_path: Path) -> Dict[str, Any]:
        """
        解析分数日志
        
        Args:
            score_path: 分数日志路径
            
        Returns:
            分数信息字典
        """
        scores = {}
        content = self._safe_read_file(score_path)
        
        if content:
            # 根据实际的scoreLOG.log格式解析
            # 这里需要根据实际格式来实现
            pass
        
        return scores
    
    def _parse_player_info(self, content: str) -> Dict[str, Any]:
        """
        解析玩家信息
        
        Args:
            content: 日志内容
            
        Returns:
            玩家信息字典
        """
        # 根据实际的pinfo.log格式解析
        return {}
    
    def _parse_hero_info(self, content: str) -> Dict[str, Any]:
        """
        解析英雄信息
        
        Args:
            content: 日志内容
            
        Returns:
            英雄信息字典
        """
        # 根据实际的hinfo.log格式解析
        return {}
