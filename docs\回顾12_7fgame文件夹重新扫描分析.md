# 回顾12: 7fgame文件夹重新扫描分析

## 分析目标
根据起凡自动化说明和起凡游戏问题及逻辑说明，重新扫描7fgame文件夹，识别读取对应的需求数据，不修改数据，重新设计数据读取架构。

## 7fgame文件夹结构分析

### 1. 核心配置文件
- **7FGame.ini**: 主配置文件，包含登录信息、窗口设置等
  - LoginName: 当前登录用户名 (hamapi003)
  - LoginID: 用户ID (********)
  - GamePath: 游戏路径
  - 窗口尺寸和状态信息

### 2. 数据库文件
- **Account.db**: 账号数据库（加密存储）
- **Chat.db**: 聊天记录数据库
- **7faac.db**: 其他游戏数据

### 3. 战功任务数据
- **Data/ZhanGongTask.json**: 战功任务定义
  - 包含17种不同类型的任务
  - 每个任务有Id、Desc（描述）、Value（目标值）、Flag（标志）
- **Data/zhangong.json**: 战功配置和治疗英雄列表
  - cure_heros: 治疗英雄ID列表 "23,26,57,150,261,330,186,188,268"
  - tasks: 与ZhanGongTask.json相同的任务列表

### 4. 英雄数据
- **Data/sanguo/hero.xml**: 英雄信息配置
  - 包含所有英雄的文件名和中文名称映射
  - 共327个英雄记录
  - 包含华佗、刘备、诸葛瑾、陆逊、孙权、曹操等关键英雄

### 5. 游戏日志系统
- **game/log[PID]-[日期时间]/**: 游戏运行日志
  - init.log: 游戏初始化信息
  - hinfo.log: 英雄信息日志
  - pinfo.log: 玩家信息日志
  - cmd.log: 游戏命令日志
  - replay*.7fr: 录像文件

### 6. 游戏资源文件
- **Data/images/**: 游戏图片资源
- **Skin/**: 皮肤文件
- **Sound/**: 音效文件
- **game/core/**: 游戏核心文件

## 需求数据识别

### 1. 战功任务识别需求
根据自动化说明，需要识别以下类型的战功任务：

#### 英雄任务
- 华佗、刘备、诸葛瑾、陆逊、孙权、曹操的：
  - 助攻任务 (Id: 8, 16)
  - 完整局任务 (Id: 2)
  - 胜利局任务 (Id: 1, 3)
  - MVP任务 (Id: 9, 17)
  - 牺牲值任务 (Id: 6, 11, 14)

#### 国家英雄任务
- 蜀国英雄：华佗、刘备
- 魏国英雄：曹操
- 吴国英雄：诸葛瑾、陆逊、孙权
- 中立英雄：根据hero.xml确定

#### 任意英雄任务
- 所有类型的任务都可以用任意英雄完成

### 2. 英雄选择需求
需要从hero.xml中读取：
- 英雄中文名称到ID的映射
- 英雄国家归属判断
- 治疗英雄识别（从zhangong.json的cure_heros字段）

### 3. 游戏状态监控需求
需要从游戏日志中读取：
- 当前游戏状态
- 英雄选择情况
- 游戏结果（胜利/失败/逃跑）
- MVP值、助攻数、牺牲值等统计数据

### 4. 账号管理需求
需要从配置文件中读取：
- 当前登录账号信息
- 账号切换状态
- 登录历史记录

## 数据读取架构设计

### 1. 数据读取器基类
```python
class GameDataReader:
    """游戏数据读取器基类"""
    def __init__(self, game_path: str):
        self.game_path = Path(game_path)
    
    def read_data(self):
        """读取数据的抽象方法"""
        raise NotImplementedError
```

### 2. 具体数据读取器

#### 战功任务读取器
```python
class ZhanGongTaskReader(GameDataReader):
    """战功任务数据读取器"""
    def read_zhangong_tasks(self) -> List[Dict]:
        """读取战功任务列表"""
    
    def read_cure_heroes(self) -> List[str]:
        """读取治疗英雄列表"""
    
    def identify_task_type(self, task: Dict) -> str:
        """识别任务类型"""
```

#### 英雄数据读取器
```python
class HeroDataReader(GameDataReader):
    """英雄数据读取器"""
    def read_hero_list(self) -> List[Dict]:
        """读取英雄列表"""
    
    def get_hero_by_name(self, name: str) -> Dict:
        """根据名称获取英雄信息"""
    
    def get_heroes_by_country(self, country: str) -> List[Dict]:
        """根据国家获取英雄列表"""
```

#### 游戏状态读取器
```python
class GameStateReader(GameDataReader):
    """游戏状态读取器"""
    def read_current_game_state(self) -> Dict:
        """读取当前游戏状态"""
    
    def read_game_logs(self) -> List[Dict]:
        """读取游戏日志"""
    
    def get_latest_game_result(self) -> Dict:
        """获取最新游戏结果"""
```

#### 配置文件读取器
```python
class ConfigReader(GameDataReader):
    """配置文件读取器"""
    def read_game_config(self) -> Dict:
        """读取游戏主配置"""
    
    def read_current_account(self) -> Dict:
        """读取当前登录账号"""
    
    def read_window_settings(self) -> Dict:
        """读取窗口设置"""
```

### 3. 数据管理器
```python
class GameDataManager:
    """游戏数据管理器"""
    def __init__(self, game_path: str):
        self.zhangong_reader = ZhanGongTaskReader(game_path)
        self.hero_reader = HeroDataReader(game_path)
        self.state_reader = GameStateReader(game_path)
        self.config_reader = ConfigReader(game_path)
    
    def get_available_tasks(self) -> List[Dict]:
        """获取可用的战功任务"""
    
    def get_suitable_hero(self, task: Dict) -> Dict:
        """根据任务获取合适的英雄"""
    
    def monitor_game_progress(self) -> Dict:
        """监控游戏进度"""
```

## 下一步计划

1. **实现数据读取器**: 创建各个数据读取器类
2. **数据解析逻辑**: 实现JSON、XML、INI文件解析
3. **任务匹配算法**: 实现战功任务与英雄的匹配逻辑
4. **状态监控系统**: 实现游戏状态实时监控
5. **集成测试**: 与现有QFL系统集成测试

## 关键数据文件详细分析

### 1. ZhanGongTask.json 任务类型分析
```json
{
  "Id": 1, "Desc": "获得1局胜利", "Value": 1, "Flag": 3
  "Id": 2, "Desc": "完成1局完整游戏局", "Value": 1, "Flag": 1
  "Id": 8, "Desc": "完成20个助攻", "Value": 20, "Flag": 3
  "Id": 9, "Desc": "获得150mvp值", "Value": 150, "Flag": 3
  "Id": 6, "Desc": "获得200K牺牲值", "Value": 200000, "Flag": 3
}
```

### 2. 英雄国家归属映射
根据自动化说明和hero.xml分析：
- **蜀国**: 华佗、刘备、关羽、张飞、赵云、诸葛亮、黄忠、魏延、姜维等
- **魏国**: 曹操、夏侯惇、夏侯渊、典韦、许褚、张辽、张郃、徐晃、司马懿等
- **吴国**: 孙权、孙策、周瑜、陆逊、甘宁、黄盖、太史慈、凌统等
- **中立**: 诸葛瑾（说明中提到属于中立）

### 3. 治疗英雄ID映射
从zhangong.json的cure_heros字段："23,26,57,150,261,330,186,188,268"
需要建立ID到英雄名称的映射关系。

### 4. 游戏日志文件结构
```
game/log[PID]-[日期时间]/
├── init.log          # 游戏初始化信息
├── hinfo.log         # 英雄信息（血量、蓝量、位置等）
├── pinfo.log         # 玩家信息（等级、金币、经验等）
├── cmd.log           # 游戏命令记录
├── end.log           # 游戏结束信息
├── scoreLOG.log      # 分数统计
├── replay*.7fr       # 录像文件
└── ...
```

## 数据读取优先级

### 高优先级（实时需要）
1. 当前战功任务状态
2. 游戏内英雄状态（血量、蓝量、位置）
3. 游戏进行状态（是否在游戏中）
4. 当前选择的英雄

### 中优先级（定期读取）
1. 战功任务完成进度
2. 游戏结果统计
3. 账号状态信息

### 低优先级（启动时读取）
1. 英雄列表和属性
2. 任务定义和规则
3. 配置文件设置

## 技术要点

1. **只读操作**: 所有数据读取都是只读的，不修改原始文件
2. **异常处理**: 完善的异常处理机制，防止文件读取失败
3. **缓存机制**: 对频繁读取的数据进行缓存优化
4. **实时监控**: 支持游戏状态的实时监控和更新
5. **数据验证**: 对读取的数据进行有效性验证
6. **文件锁处理**: 处理游戏运行时的文件锁定问题
7. **编码处理**: 正确处理中文编码（UTF-8、GBK等）

## 实施建议

### 阶段1: 基础数据读取器
- 实现配置文件读取器（INI格式）
- 实现JSON数据读取器
- 实现XML数据读取器

### 阶段2: 游戏状态监控
- 实现日志文件监控
- 实现游戏进程状态检测
- 实现实时数据更新机制

### 阶段3: 智能任务匹配
- 实现任务类型识别算法
- 实现英雄选择推荐算法
- 实现任务优先级排序

### 阶段4: 集成优化
- 与现有QFL系统集成
- 性能优化和缓存策略
- 错误处理和恢复机制

---
**开发者**: @ConceptualGod
**完成时间**: 2025-07-28
**状态**: 详细分析完成，准备开始实施
