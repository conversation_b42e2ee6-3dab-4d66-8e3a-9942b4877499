#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
起凡战功识别系统启动器

提供命令行和GUI两种界面选择
开发者: @ConceptualGod
创建时间: 2025-07-28
"""

import sys
import argparse
import logging
from pathlib import Path

# 添加项目路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))


def setup_logging():
    """
    设置日志配置
    
    开发者: @ConceptualGod
    """
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('zhangong_system.log', encoding='utf-8')
        ]
    )


def show_banner():
    """
    显示程序横幅
    
    开发者: @ConceptualGod
    """
    banner = """
    ================================================
    起凡战功识别系统 By @ConceptualGod
    ================================================
    
    功能特性:
    - 实时内存读取游戏内战功任务
    - 支持命令行和GUI两种界面
    - 自动监控任务进度变化
    - 详细的日志记录和统计
    
    支持的任务类型:
    - 助攻任务
    - 完整局任务  
    - 胜利局任务
    - MVP任务
    - 牺牲值任务
    
    ================================================
    """
    print(banner)


def check_requirements():
    """
    检查运行环境
    
    Returns:
        是否满足要求
        
    开发者: @ConceptualGod
    """
    try:
        import psutil
        import ctypes
        print("环境检查通过 - By @ConceptualGod")
        return True
    except ImportError as e:
        print(f"缺少必要的依赖库: {e}")
        print("请安装: pip install psutil")
        return False


def run_cli_mode():
    """
    运行命令行模式
    
    开发者: @ConceptualGod
    """
    try:
        from zhangong_cli import ZhanGongCLI
        
        print("启动命令行界面...")
        cli = ZhanGongCLI()
        cli.run()
        
    except ImportError as e:
        print(f"导入命令行模块失败: {e}")
    except Exception as e:
        print(f"命令行模式运行异常: {e}")


def run_gui_mode():
    """
    运行GUI模式
    
    开发者: @ConceptualGod
    """
    try:
        from zhangong_gui import ZhanGongGUI
        
        print("启动图形界面...")
        gui = ZhanGongGUI()
        gui.run()
        
    except ImportError as e:
        print(f"导入GUI模块失败: {e}")
        print("可能缺少tkinter支持")
    except Exception as e:
        print(f"GUI模式运行异常: {e}")


def interactive_mode():
    """
    交互式选择模式
    
    开发者: @ConceptualGod
    """
    while True:
        print("\n请选择运行模式:")
        print("1. 命令行界面 (CLI)")
        print("2. 图形界面 (GUI)")
        print("0. 退出程序")
        
        try:
            choice = input("\n请输入选择 (0-2): ").strip()
            
            if choice == "0":
                print("感谢使用起凡战功识别系统 - By @ConceptualGod")
                break
            elif choice == "1":
                run_cli_mode()
                break
            elif choice == "2":
                run_gui_mode()
                break
            else:
                print("无效选择，请重新输入")
                
        except KeyboardInterrupt:
            print("\n\n程序被用户中断")
            break
        except Exception as e:
            print(f"输入处理异常: {e}")


def main():
    """
    主函数
    
    开发者: @ConceptualGod
    """
    # 设置命令行参数
    parser = argparse.ArgumentParser(
        description="起凡战功识别系统 - By @ConceptualGod",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  python zhangong_launcher.py          # 交互式选择模式
  python zhangong_launcher.py --cli    # 直接启动命令行界面
  python zhangong_launcher.py --gui    # 直接启动图形界面
  
开发者: @ConceptualGod
        """
    )
    
    parser.add_argument('--cli', action='store_true', 
                       help='直接启动命令行界面')
    parser.add_argument('--gui', action='store_true', 
                       help='直接启动图形界面')
    parser.add_argument('--version', action='version', 
                       version='起凡战功识别系统 v1.0 - By @ConceptualGod')
    
    args = parser.parse_args()
    
    # 设置日志
    setup_logging()
    
    # 显示横幅
    show_banner()
    
    # 检查环境
    if not check_requirements():
        input("按回车键退出...")
        return
    
    try:
        # 根据参数选择运行模式
        if args.cli:
            run_cli_mode()
        elif args.gui:
            run_gui_mode()
        else:
            interactive_mode()
            
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"程序运行异常: {e}")
        logging.error(f"程序运行异常 - By @ConceptualGod: {e}")
    
    finally:
        print("程序已退出 - By @ConceptualGod")


if __name__ == "__main__":
    main()
