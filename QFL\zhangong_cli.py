#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
起凡战功识别命令行界面

通过内存读取实时识别游戏内战功任务
开发者: @ConceptualGod
创建时间: 2025-07-28
"""

import time
import logging
import threading
from datetime import datetime
from typing import List, Dict, Any
from core.memory_reader import GameMemoryReader, ZhanGongTask


class ZhanGongCLI:
    """
    战功识别命令行界面
    
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化命令行界面
        
        开发者: @ConceptualGod
        """
        self.memory_reader = GameMemoryReader()
        self.logger = logging.getLogger(__name__)
        self.is_monitoring = False
        self.monitor_thread = None
        self.last_tasks = []
        
        # 设置日志格式
        self.setup_logging()
        
        print("起凡战功识别系统 By @ConceptualGod")
        print("=" * 50)
        
    def setup_logging(self):
        """
        设置日志配置
        
        开发者: @ConceptualGod
        """
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.StreamHandler(),
                logging.FileHandler('zhangong_monitor.log', encoding='utf-8')
            ]
        )
        
        self.logger.info("日志系统初始化完成 - By @ConceptualGod")
    
    def show_menu(self):
        """
        显示主菜单
        
        开发者: @ConceptualGod
        """
        print("\n" + "=" * 50)
        print("战功识别系统主菜单 - By @ConceptualGod")
        print("=" * 50)
        print("1. 检查游戏状态")
        print("2. 读取当前战功任务")
        print("3. 开始实时监控")
        print("4. 停止实时监控")
        print("5. 显示内存信息")
        print("6. 显示任务统计")
        print("0. 退出程序")
        print("=" * 50)
    
    def check_game_status(self):
        """
        检查游戏状态
        
        开发者: @ConceptualGod
        """
        print("\n检查游戏状态中...")
        
        if self.memory_reader.find_game_process():
            process_info = self.memory_reader.get_memory_info()
            print(f"游戏状态: 运行中")
            print(f"进程名称: {process_info['process_name']}")
            print(f"进程ID: {process_info['process_pid']}")
            self.logger.info("游戏状态检查完成 - By @ConceptualGod")
        else:
            print("游戏状态: 未运行")
            print("请先启动起凡游戏")
            self.logger.warning("游戏未运行 - By @ConceptualGod")
    
    def read_current_tasks(self):
        """
        读取当前战功任务
        
        开发者: @ConceptualGod
        """
        print("\n正在读取战功任务...")
        
        try:
            tasks = self.memory_reader.read_zhangong_tasks()
            
            if not tasks:
                print("未能读取到战功任务数据")
                print("可能原因:")
                print("1. 游戏未运行")
                print("2. 未进入游戏主界面")
                print("3. 内存地址需要更新")
                return
            
            self.last_tasks = tasks
            self.display_tasks(tasks)
            
        except Exception as e:
            print(f"读取任务失败: {str(e)}")
            self.logger.error(f"读取任务异常 - By @ConceptualGod: {str(e)}")
    
    def display_tasks(self, tasks: List[ZhanGongTask]):
        """
        显示任务列表
        
        Args:
            tasks: 任务列表
            
        开发者: @ConceptualGod
        """
        if not tasks:
            print("暂无战功任务数据")
            return
        
        print(f"\n战功任务列表 (共{len(tasks)}个) - By @ConceptualGod")
        print("-" * 80)
        print(f"{'ID':<4} {'类型':<12} {'进度':<15} {'状态':<8} {'任务描述'}")
        print("-" * 80)
        
        for task in tasks:
            progress_str = f"{task.current_progress}/{task.target_progress}"
            status_str = "已完成" if task.is_completed else "进行中"
            
            print(f"{task.task_id:<4} {task.task_type:<12} {progress_str:<15} {status_str:<8} {task.task_desc}")
        
        print("-" * 80)
        
        # 统计信息
        completed_count = sum(1 for task in tasks if task.is_completed)
        print(f"统计: 总任务 {len(tasks)} 个, 已完成 {completed_count} 个, 进行中 {len(tasks) - completed_count} 个")
    
    def start_monitoring(self):
        """
        开始实时监控
        
        开发者: @ConceptualGod
        """
        if self.is_monitoring:
            print("监控已在运行中")
            return
        
        print("\n开始实时监控战功任务...")
        print("按 Ctrl+C 或选择菜单选项4停止监控")
        
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(target=self.monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        self.logger.info("实时监控已启动 - By @ConceptualGod")
    
    def stop_monitoring(self):
        """
        停止实时监控
        
        开发者: @ConceptualGod
        """
        if not self.is_monitoring:
            print("监控未在运行")
            return
        
        print("\n正在停止监控...")
        self.is_monitoring = False
        
        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=2)
        
        print("监控已停止")
        self.logger.info("实时监控已停止 - By @ConceptualGod")
    
    def monitor_loop(self):
        """
        监控循环
        
        开发者: @ConceptualGod
        """
        try:
            while self.is_monitoring:
                current_time = datetime.now().strftime("%H:%M:%S")
                
                try:
                    tasks = self.memory_reader.read_zhangong_tasks()
                    
                    if tasks:
                        # 检查任务变化
                        changes = self.detect_task_changes(self.last_tasks, tasks)
                        
                        if changes:
                            print(f"\n[{current_time}] 检测到任务变化 - By @ConceptualGod")
                            for change in changes:
                                print(f"  {change}")
                        
                        self.last_tasks = tasks
                        
                        # 显示当前状态
                        completed_count = sum(1 for task in tasks if task.is_completed)
                        print(f"[{current_time}] 任务状态: {completed_count}/{len(tasks)} 已完成")
                    
                    else:
                        print(f"[{current_time}] 无法读取任务数据")
                
                except Exception as e:
                    print(f"[{current_time}] 监控异常: {str(e)}")
                    self.logger.error(f"监控循环异常 - By @ConceptualGod: {str(e)}")
                
                # 等待5秒后继续监控
                for _ in range(50):  # 分成50个0.1秒，便于快速响应停止信号
                    if not self.is_monitoring:
                        break
                    time.sleep(0.1)
                
        except KeyboardInterrupt:
            print("\n监控被用户中断")
            self.is_monitoring = False
    
    def detect_task_changes(self, old_tasks: List[ZhanGongTask], new_tasks: List[ZhanGongTask]) -> List[str]:
        """
        检测任务变化
        
        Args:
            old_tasks: 旧任务列表
            new_tasks: 新任务列表
            
        Returns:
            变化描述列表
            
        开发者: @ConceptualGod
        """
        changes = []
        
        if not old_tasks:
            return changes
        
        # 创建任务字典便于比较
        old_dict = {task.task_id: task for task in old_tasks}
        new_dict = {task.task_id: task for task in new_tasks}
        
        for task_id, new_task in new_dict.items():
            if task_id in old_dict:
                old_task = old_dict[task_id]
                
                # 检查进度变化
                if new_task.current_progress != old_task.current_progress:
                    changes.append(f"任务 {new_task.task_desc}: 进度 {old_task.current_progress} -> {new_task.current_progress}")
                
                # 检查完成状态变化
                if new_task.is_completed != old_task.is_completed:
                    status = "完成" if new_task.is_completed else "重置"
                    changes.append(f"任务 {new_task.task_desc}: {status}")
            else:
                changes.append(f"新增任务: {new_task.task_desc}")
        
        # 检查删除的任务
        for task_id, old_task in old_dict.items():
            if task_id not in new_dict:
                changes.append(f"移除任务: {old_task.task_desc}")
        
        return changes
    
    def show_memory_info(self):
        """
        显示内存信息
        
        开发者: @ConceptualGod
        """
        print("\n内存读取信息 - By @ConceptualGod")
        print("-" * 40)
        
        info = self.memory_reader.get_memory_info()
        
        for key, value in info.items():
            print(f"{key}: {value}")
        
        print("-" * 40)
    
    def show_task_statistics(self):
        """
        显示任务统计
        
        开发者: @ConceptualGod
        """
        if not self.last_tasks:
            print("暂无任务数据，请先读取任务")
            return
        
        print("\n任务统计信息 - By @ConceptualGod")
        print("-" * 40)
        
        # 按类型统计
        type_stats = {}
        for task in self.last_tasks:
            task_type = task.task_type
            if task_type not in type_stats:
                type_stats[task_type] = {"total": 0, "completed": 0}
            
            type_stats[task_type]["total"] += 1
            if task.is_completed:
                type_stats[task_type]["completed"] += 1
        
        print("按类型统计:")
        for task_type, stats in type_stats.items():
            completion_rate = (stats["completed"] / stats["total"]) * 100 if stats["total"] > 0 else 0
            print(f"  {task_type}: {stats['completed']}/{stats['total']} ({completion_rate:.1f}%)")
        
        print("-" * 40)
    
    def run(self):
        """
        运行命令行界面
        
        开发者: @ConceptualGod
        """
        try:
            while True:
                self.show_menu()
                
                try:
                    choice = input("\n请选择操作 (0-6): ").strip()
                    
                    if choice == "0":
                        print("感谢使用起凡战功识别系统 - By @ConceptualGod")
                        break
                    elif choice == "1":
                        self.check_game_status()
                    elif choice == "2":
                        self.read_current_tasks()
                    elif choice == "3":
                        self.start_monitoring()
                    elif choice == "4":
                        self.stop_monitoring()
                    elif choice == "5":
                        self.show_memory_info()
                    elif choice == "6":
                        self.show_task_statistics()
                    else:
                        print("无效选择，请重新输入")
                
                except KeyboardInterrupt:
                    print("\n\n程序被用户中断")
                    break
                except Exception as e:
                    print(f"操作异常: {str(e)}")
                    self.logger.error(f"操作异常 - By @ConceptualGod: {str(e)}")
        
        finally:
            # 清理资源
            if self.is_monitoring:
                self.stop_monitoring()
            
            self.memory_reader.close_process_handle()
            print("程序已退出 - By @ConceptualGod")


def main():
    """
    主函数
    
    开发者: @ConceptualGod
    """
    try:
        cli = ZhanGongCLI()
        cli.run()
    except Exception as e:
        print(f"程序启动失败: {str(e)}")
        input("按回车键退出...")


if __name__ == "__main__":
    main()
