# 回顾13: 重新设计游戏数据读取架构

## 设计原则
严格按照起凡自动化说明.md的要求，只识别和处理明确提到的数据，不做额外扩展。

## 明确的需求范围

### 1. 目标英雄（6个）
根据自动化说明，只需要处理以下6个英雄：
- **华佗**（蜀国）- 技能：W和D
- **刘备**（蜀国）- 技能：C和E  
- **曹操**（魏国）- 技能：C
- **诸葛瑾**（中立）- 技能：E和W
- **陆逊**（中立）- 技能：E
- **孙权**（中立）- 技能：E

### 2. 任务类型（5种）
只识别以下5种任务类型：
- **助攻任务**
- **完整局任务**
- **胜利局任务** 
- **MVP任务**
- **牺牲值任务**

### 3. 国家分类（3个）
- **蜀国**：华佗、刘备
- **魏国**：曹操
- **中立**：诸葛瑾、陆逊、孙权

## 实现的数据读取架构

### 1. 基础数据读取器 (game_data_reader.py)

#### GameDataReader (基类)
- 提供安全的文件读取方法
- 处理编码问题（UTF-8/GBK）
- 统一的异常处理

#### ZhanGongTaskReader (战功任务读取器)
```python
- read_zhangong_tasks(): 读取ZhanGongTask.json
- read_cure_heroes(): 读取治疗英雄ID列表
- identify_task_type(): 识别5种目标任务类型
```

#### HeroDataReader (英雄数据读取器)
```python
- read_hero_list(): 读取hero.xml中的英雄信息
- _determine_hero_country(): 判断6个目标英雄的国家归属
- get_hero_by_name(): 根据名称获取英雄信息
```

#### ConfigReader (配置文件读取器)
```python
- read_game_config(): 读取7FGame.ini配置
- read_current_account(): 获取当前登录账号
- read_window_settings(): 读取窗口设置
```

### 2. 数据管理器 (game_data_manager.py)

#### GameDataManager (核心管理器)
整合所有数据读取器，提供业务逻辑：

```python
- get_available_tasks(): 获取5种目标任务类型的任务
- get_suitable_hero_for_task(): 为任务推荐合适的英雄
- get_hero_by_country(): 根据国家获取英雄列表
- check_task_hero_match(): 检查任务和英雄匹配性
- get_hero_skills_for_task(): 获取英雄技能配置
- validate_game_data(): 验证数据完整性
```

## 关键设计特点

### 1. 严格限制范围
- 只处理明确提到的6个英雄
- 只识别明确提到的5种任务类型
- 只使用明确提到的3个国家分类

### 2. 只读操作
- 所有操作都是只读的，不修改游戏文件
- 安全的文件访问，处理文件锁定问题
- 完善的异常处理机制

### 3. 数据验证
- 验证目标英雄是否都能在hero.xml中找到
- 验证任务数据是否可读
- 验证配置文件是否正常

### 4. 业务逻辑集中
- 英雄技能配置集中在数据管理器中
- 任务-英雄匹配逻辑统一管理
- 国家归属判断逻辑明确

## 数据文件映射

### 1. 战功任务数据
- **源文件**: `7fgame/Data/ZhanGongTask.json`
- **用途**: 获取所有任务定义，筛选出5种目标任务类型
- **关键字段**: Id, Desc, Value, Flag

### 2. 治疗英雄数据  
- **源文件**: `7fgame/Data/zhangong.json`
- **用途**: 获取cure_heros字段，识别治疗英雄
- **关键信息**: 华佗为治疗英雄

### 3. 英雄信息数据
- **源文件**: `7fgame/Data/sanguo/hero.xml`
- **用途**: 获取英雄名称列表，验证6个目标英雄存在
- **关键字段**: HeroName, FileName

### 4. 游戏配置数据
- **源文件**: `7fgame/7FGame.ini`
- **用途**: 获取当前登录账号、窗口设置等
- **关键字段**: LoginName, LoginID, MainWndW, MainWndH

## 使用示例

```python
# 初始化数据管理器
manager = GameDataManager("path/to/7fgame")

# 获取可用任务
tasks = manager.get_available_tasks()

# 为任务推荐英雄
for task in tasks:
    hero = manager.get_suitable_hero_for_task(task)
    skills = manager.get_hero_skills_for_task(hero)
    print(f"任务: {task['Desc']}, 推荐英雄: {hero}, 技能: {skills}")

# 验证数据完整性
validation = manager.validate_game_data()
print(f"数据验证结果: {validation}")
```

## 下一步计划

1. **集成到QFL系统**: 将新的数据读取架构集成到现有QFL项目
2. **实时监控**: 实现游戏状态的实时监控功能
3. **任务匹配算法**: 完善任务-英雄匹配的智能算法
4. **测试验证**: 对所有数据读取功能进行全面测试

## 技术优势

1. **精确匹配需求**: 严格按照自动化说明实现，不做无关扩展
2. **高效数据访问**: 只读取必要的数据，提高性能
3. **易于维护**: 清晰的模块划分，便于后续维护
4. **安全可靠**: 完善的异常处理，不会影响游戏运行

---
**开发者**: @ConceptualGod  
**完成时间**: 2025-07-28  
**状态**: 架构设计完成，准备集成测试
