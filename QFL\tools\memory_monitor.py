#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
起凡内存实时监控工具

监控指定内存地址的变化，用于分析战功任务数据结构
开发者: @ConceptualGod
创建时间: 2025-07-28
"""

import ctypes
import struct
import time
import threading
import sys
import os
from typing import List, Dict, Any, Optional, Callable

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))
from memory_analyzer import GameMemoryAnalyzer


class MemoryMonitor:
    """
    内存监控器
    
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化监控器
        
        开发者: @ConceptualGod
        """
        self.analyzer = GameMemoryAnalyzer()
        self.monitoring = False
        self.monitor_thread = None
        self.watch_addresses = []
        self.change_callbacks = []
        self.last_values = {}
        
        print("内存监控器初始化完成 - By @ConceptualGod")
    
    def add_watch_address(self, address: int, size: int, name: str = "", data_type: str = "bytes"):
        """
        添加监控地址
        
        Args:
            address: 内存地址
            size: 监控大小
            name: 地址名称
            data_type: 数据类型 (bytes, int32, int64, string)
            
        开发者: @ConceptualGod
        """
        watch_info = {
            'address': address,
            'size': size,
            'name': name or f"0x{address:X}",
            'data_type': data_type
        }
        
        self.watch_addresses.append(watch_info)
        print(f"添加监控地址: {watch_info['name']} (0x{address:X}) - By @ConceptualGod")
    
    def add_change_callback(self, callback: Callable):
        """
        添加变化回调函数
        
        Args:
            callback: 回调函数
            
        开发者: @ConceptualGod
        """
        self.change_callbacks.append(callback)
    
    def start_monitoring(self, interval: float = 1.0):
        """
        开始监控
        
        Args:
            interval: 监控间隔（秒）
            
        开发者: @ConceptualGod
        """
        if self.monitoring:
            print("监控已在运行中 - By @ConceptualGod")
            return
        
        # 初始化游戏连接
        if not self.analyzer.find_game_process():
            print("未找到游戏进程 - By @ConceptualGod")
            return False
        
        if not self.analyzer.open_process_handle():
            print("无法打开进程句柄 - By @ConceptualGod")
            return False
        
        self.monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitor_loop,
            args=(interval,),
            daemon=True
        )
        self.monitor_thread.start()
        
        print(f"开始监控 {len(self.watch_addresses)} 个地址 - By @ConceptualGod")
        return True
    
    def stop_monitoring(self):
        """
        停止监控
        
        开发者: @ConceptualGod
        """
        if not self.monitoring:
            return
        
        self.monitoring = False
        if self.monitor_thread:
            self.monitor_thread.join(timeout=2.0)
        
        self.analyzer.close_handle()
        print("监控已停止 - By @ConceptualGod")
    
    def _monitor_loop(self, interval: float):
        """
        监控循环
        
        Args:
            interval: 监控间隔
            
        开发者: @ConceptualGod
        """
        try:
            while self.monitoring:
                changes = self._check_changes()
                
                if changes:
                    self._notify_changes(changes)
                
                time.sleep(interval)
                
        except Exception as e:
            print(f"监控循环异常: {e} - By @ConceptualGod")
        finally:
            self.monitoring = False
    
    def _check_changes(self) -> List[Dict[str, Any]]:
        """
        检查内存变化
        
        Returns:
            变化列表
            
        开发者: @ConceptualGod
        """
        changes = []
        
        for watch_info in self.watch_addresses:
            address = watch_info['address']
            size = watch_info['size']
            name = watch_info['name']
            data_type = watch_info['data_type']
            
            # 读取当前值
            raw_data = self.analyzer.read_memory_safe(address, size)
            if raw_data is None:
                continue
            
            # 解析数据
            current_value = self._parse_data(raw_data, data_type)
            
            # 检查是否有变化
            last_value = self.last_values.get(address)
            if last_value != current_value:
                change_info = {
                    'address': address,
                    'name': name,
                    'data_type': data_type,
                    'old_value': last_value,
                    'new_value': current_value,
                    'raw_data': raw_data.hex(),
                    'timestamp': time.time()
                }
                changes.append(change_info)
                self.last_values[address] = current_value
        
        return changes
    
    def _parse_data(self, data: bytes, data_type: str) -> Any:
        """
        解析数据
        
        Args:
            data: 原始数据
            data_type: 数据类型
            
        Returns:
            解析后的值
            
        开发者: @ConceptualGod
        """
        try:
            if data_type == "int32" and len(data) >= 4:
                return struct.unpack('<I', data[:4])[0]
            elif data_type == "int64" and len(data) >= 8:
                return struct.unpack('<Q', data[:8])[0]
            elif data_type == "string":
                # 尝试不同编码
                for encoding in ['utf-8', 'gbk', 'utf-16le']:
                    try:
                        # 查找字符串结束符
                        if encoding == 'utf-16le':
                            end_pos = data.find(b'\x00\x00')
                            if end_pos == -1:
                                end_pos = len(data)
                            return data[:end_pos].decode(encoding)
                        else:
                            end_pos = data.find(b'\x00')
                            if end_pos == -1:
                                end_pos = len(data)
                            return data[:end_pos].decode(encoding)
                    except:
                        continue
                return data.hex()  # 如果无法解码，返回十六进制
            else:  # bytes
                return data.hex()
        except:
            return data.hex()
    
    def _notify_changes(self, changes: List[Dict[str, Any]]):
        """
        通知变化
        
        Args:
            changes: 变化列表
            
        开发者: @ConceptualGod
        """
        for change in changes:
            print(f"[{time.strftime('%H:%M:%S')}] 内存变化 - {change['name']}")
            print(f"  地址: 0x{change['address']:X}")
            print(f"  旧值: {change['old_value']}")
            print(f"  新值: {change['new_value']}")
            print(f"  原始数据: {change['raw_data']}")
            print()
        
        # 调用回调函数
        for callback in self.change_callbacks:
            try:
                callback(changes)
            except Exception as e:
                print(f"回调函数异常: {e} - By @ConceptualGod")
    
    def scan_and_monitor_tasks(self):
        """
        扫描并监控战功任务
        
        开发者: @ConceptualGod
        """
        print("扫描战功任务地址 - By @ConceptualGod")
        
        # 搜索已知任务描述
        task_keywords = [
            "助攻", "胜利", "完整", "MVP", "牺牲值",
            "华佗", "刘备", "曹操", "诸葛瑾", "陆逊", "孙权"
        ]
        
        found_addresses = []
        
        for keyword in task_keywords:
            print(f"搜索关键字: {keyword}")
            
            for encoding in ['utf-8', 'gbk', 'utf-16le']:
                try:
                    addresses = self.analyzer.search_string(keyword, encoding)
                    if addresses:
                        print(f"  找到 {len(addresses)} 个位置 ({encoding})")
                        found_addresses.extend(addresses)
                except:
                    continue
        
        # 去重并排序
        unique_addresses = sorted(list(set(found_addresses)))
        print(f"总共找到 {len(unique_addresses)} 个唯一地址")
        
        # 添加监控地址（监控每个地址周围的内存）
        for i, addr in enumerate(unique_addresses[:20]):  # 只监控前20个
            # 监控任务描述
            self.add_watch_address(addr, 64, f"任务描述_{i}", "string")
            
            # 监控前面可能的ID和进度
            self.add_watch_address(addr - 16, 4, f"可能ID_{i}", "int32")
            self.add_watch_address(addr - 12, 4, f"可能进度1_{i}", "int32")
            self.add_watch_address(addr - 8, 4, f"可能进度2_{i}", "int32")
            self.add_watch_address(addr - 4, 4, f"可能状态_{i}", "int32")
        
        print(f"设置监控 {len(self.watch_addresses)} 个地址")
    
    def interactive_monitor(self):
        """
        交互式监控
        
        开发者: @ConceptualGod
        """
        print("起凡内存实时监控工具 By @ConceptualGod")
        print("=" * 50)
        
        while True:
            print("\n选择操作:")
            print("1. 扫描并监控战功任务")
            print("2. 手动添加监控地址")
            print("3. 开始监控")
            print("4. 停止监控")
            print("5. 显示当前监控地址")
            print("0. 退出")
            
            try:
                choice = input("\n请选择 (0-5): ").strip()
                
                if choice == "0":
                    break
                elif choice == "1":
                    if not self.analyzer.find_game_process():
                        print("请先启动游戏")
                        continue
                    if not self.analyzer.open_process_handle():
                        print("无法打开进程句柄，请以管理员身份运行")
                        continue
                    self.scan_and_monitor_tasks()
                elif choice == "2":
                    self._add_manual_address()
                elif choice == "3":
                    if not self.watch_addresses:
                        print("请先添加监控地址")
                        continue
                    interval = float(input("监控间隔(秒，默认1.0): ") or "1.0")
                    self.start_monitoring(interval)
                elif choice == "4":
                    self.stop_monitoring()
                elif choice == "5":
                    self._show_watch_addresses()
                else:
                    print("无效选择")
                    
            except KeyboardInterrupt:
                print("\n用户中断")
                break
            except Exception as e:
                print(f"操作异常: {e}")
        
        self.stop_monitoring()
        print("监控工具已退出 - By @ConceptualGod")
    
    def _add_manual_address(self):
        """
        手动添加监控地址
        
        开发者: @ConceptualGod
        """
        try:
            addr_str = input("输入地址 (十六进制，如 0x12345678): ").strip()
            if addr_str.startswith('0x'):
                address = int(addr_str, 16)
            else:
                address = int(addr_str, 16)
            
            size = int(input("输入大小 (字节数): "))
            name = input("输入名称 (可选): ").strip()
            
            print("数据类型:")
            print("1. bytes (原始字节)")
            print("2. int32 (32位整数)")
            print("3. int64 (64位整数)")
            print("4. string (字符串)")
            
            type_choice = input("选择类型 (1-4): ").strip()
            type_map = {"1": "bytes", "2": "int32", "3": "int64", "4": "string"}
            data_type = type_map.get(type_choice, "bytes")
            
            self.add_watch_address(address, size, name, data_type)
            
        except Exception as e:
            print(f"添加地址失败: {e}")
    
    def _show_watch_addresses(self):
        """
        显示当前监控地址
        
        开发者: @ConceptualGod
        """
        if not self.watch_addresses:
            print("当前没有监控地址")
            return
        
        print(f"\n当前监控地址 ({len(self.watch_addresses)} 个):")
        print("-" * 60)
        
        for i, watch_info in enumerate(self.watch_addresses):
            print(f"{i+1:2d}. {watch_info['name']}")
            print(f"     地址: 0x{watch_info['address']:X}")
            print(f"     大小: {watch_info['size']} 字节")
            print(f"     类型: {watch_info['data_type']}")
            
            # 显示当前值
            current_value = self.last_values.get(watch_info['address'])
            if current_value is not None:
                print(f"     当前值: {current_value}")
            print()


def main():
    """
    主函数
    
    开发者: @ConceptualGod
    """
    monitor = MemoryMonitor()
    monitor.interactive_monitor()


if __name__ == "__main__":
    main()
