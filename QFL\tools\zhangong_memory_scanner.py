#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
起凡战功内存扫描器

专门用于扫描和分析战功任务的内存结构
开发者: @ConceptualGod
创建时间: 2025-07-28
"""

import ctypes
import struct
import time
import json
import sys
import os
from typing import List, Dict, Any, Optional

# 添加当前目录到路径
sys.path.append(os.path.dirname(__file__))
from memory_analyzer import GameMemoryAnalyzer


class ZhanGongMemoryScanner:
    """
    战功内存扫描器
    
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化扫描器
        
        开发者: @ConceptualGod
        """
        self.analyzer = GameMemoryAnalyzer()
        self.task_candidates = []
        self.verified_structures = []
        
        print("战功内存扫描器初始化完成 - By @ConceptualGod")
    
    def scan_for_task_data(self) -> bool:
        """
        扫描战功任务数据
        
        Returns:
            是否成功扫描
            
        开发者: @ConceptualGod
        """
        print("开始扫描战功任务数据 - By @ConceptualGod")
        
        # 1. 查找游戏进程
        if not self.analyzer.find_game_process():
            return False
        
        # 2. 打开进程句柄
        if not self.analyzer.open_process_handle():
            return False
        
        # 3. 搜索已知的战功任务描述
        known_tasks = [
            "使用华佗获得助攻",
            "使用刘备获得助攻", 
            "使用诸葛瑾获得助攻",
            "使用陆逊获得助攻",
            "使用孙权获得助攻",
            "使用曹操获得助攻",
            "使用华佗完整游戏",
            "使用刘备完整游戏",
            "使用华佗获得胜利",
            "使用刘备获得胜利",
            "获得MVP",
            "牺牲值"
        ]
        
        task_addresses = {}
        
        for task_desc in known_tasks:
            print(f"搜索任务: {task_desc}")
            
            # 尝试不同编码搜索
            for encoding in ['utf-8', 'gbk', 'utf-16le']:
                try:
                    addresses = self.analyzer.search_string(task_desc, encoding)
                    if addresses:
                        task_addresses[task_desc] = {
                            'encoding': encoding,
                            'addresses': addresses
                        }
                        print(f"  找到 {len(addresses)} 个位置 ({encoding})")
                        break
                except:
                    continue
        
        # 4. 分析找到的任务地址
        self.analyze_task_addresses(task_addresses)
        
        return len(task_addresses) > 0
    
    def analyze_task_addresses(self, task_addresses: Dict[str, Dict]):
        """
        分析任务地址周围的内存结构
        
        Args:
            task_addresses: 任务地址字典
            
        开发者: @ConceptualGod
        """
        print("分析任务内存结构 - By @ConceptualGod")
        
        for task_desc, info in task_addresses.items():
            print(f"\n分析任务: {task_desc}")
            
            for addr in info['addresses'][:3]:  # 只分析前3个地址
                # 分析任务结构体
                structure = self.analyze_task_structure(addr, task_desc)
                if structure:
                    self.task_candidates.append(structure)
    
    def analyze_task_structure(self, address: int, task_desc: str) -> Optional[Dict[str, Any]]:
        """
        分析单个任务结构体
        
        Args:
            address: 任务描述地址
            task_desc: 任务描述文本
            
        Returns:
            任务结构信息
            
        开发者: @ConceptualGod
        """
        # 读取任务描述前后的内存
        before_data = self.analyzer.read_memory_safe(address - 512, 512)
        after_data = self.analyzer.read_memory_safe(address, 1024)
        
        if not before_data or not after_data:
            return None
        
        structure = {
            'task_desc': task_desc,
            'desc_address': f"0x{address:X}",
            'possible_task_id': None,
            'possible_progress': None,
            'possible_target': None,
            'possible_status': None,
            'structure_start': None,
            'structure_size': None
        }
        
        # 在描述前查找可能的任务ID（通常是小整数）
        for offset in range(0, 512, 4):
            try:
                value = struct.unpack('<I', before_data[offset:offset+4])[0]
                if 1 <= value <= 100:  # 任务ID通常在这个范围
                    structure['possible_task_id'] = {
                        'value': value,
                        'offset': offset - 512,
                        'address': f"0x{address + offset - 512:X}"
                    }
                    break
            except:
                continue
        
        # 在描述后查找可能的进度值
        progress_candidates = []
        for offset in range(0, min(200, len(after_data) - 4), 4):
            try:
                value = struct.unpack('<I', after_data[offset:offset+4])[0]
                if 0 <= value <= 1000:  # 进度值通常在这个范围
                    progress_candidates.append({
                        'value': value,
                        'offset': offset,
                        'address': f"0x{address + offset:X}"
                    })
            except:
                continue
        
        if len(progress_candidates) >= 2:
            # 假设前两个是当前进度和目标进度
            structure['possible_progress'] = progress_candidates[0]
            structure['possible_target'] = progress_candidates[1]
        
        # 查找可能的状态标志（布尔值）
        for offset in range(0, min(100, len(after_data) - 1)):
            value = after_data[offset]
            if value in [0, 1]:  # 可能的布尔状态
                structure['possible_status'] = {
                    'value': bool(value),
                    'offset': offset,
                    'address': f"0x{address + offset:X}"
                }
                break
        
        return structure
    
    def scan_for_task_list(self) -> List[Dict[str, Any]]:
        """
        扫描任务列表结构
        
        Returns:
            任务列表信息
            
        开发者: @ConceptualGod
        """
        print("扫描任务列表结构 - By @ConceptualGod")
        
        # 搜索可能的任务数量
        task_count_candidates = []
        
        # 搜索数字5-20（可能的任务数量）
        for count in range(5, 21):
            count_bytes = struct.pack('<I', count)
            addresses = self.analyzer.search_pattern(count_bytes)
            
            if addresses:
                print(f"找到可能的任务数量 {count}: {len(addresses)} 个位置")
                task_count_candidates.extend([{
                    'count': count,
                    'address': addr
                } for addr in addresses])
        
        # 分析任务数量周围的内存
        list_structures = []
        for candidate in task_count_candidates[:10]:  # 只分析前10个
            structure = self.analyze_task_list_structure(candidate['address'], candidate['count'])
            if structure:
                list_structures.append(structure)
        
        return list_structures
    
    def analyze_task_list_structure(self, address: int, count: int) -> Optional[Dict[str, Any]]:
        """
        分析任务列表结构
        
        Args:
            address: 任务数量地址
            count: 任务数量
            
        Returns:
            列表结构信息
            
        开发者: @ConceptualGod
        """
        # 读取任务数量前后的内存
        data = self.analyzer.read_memory_safe(address - 256, 1024)
        if not data:
            return None
        
        structure = {
            'count_address': f"0x{address:X}",
            'task_count': count,
            'possible_list_start': None,
            'possible_item_size': None,
            'verified_tasks': []
        }
        
        # 在任务数量后查找可能的任务列表指针
        for offset in range(260, min(512, len(data) - 8), 4):
            try:
                # 尝试读取指针
                ptr_value = struct.unpack('<Q', data[offset:offset+8])[0]
                
                # 检查是否是有效的用户空间指针
                if 0x400000 <= ptr_value <= 0x7FFFFFFF:
                    # 尝试读取指针指向的内存
                    pointed_data = self.analyzer.read_memory_safe(ptr_value, 1024)
                    if pointed_data:
                        structure['possible_list_start'] = {
                            'pointer_address': f"0x{address + offset - 256:X}",
                            'points_to': f"0x{ptr_value:X}",
                            'data_preview': pointed_data[:64].hex()
                        }
                        break
            except:
                continue
        
        return structure
    
    def verify_task_structure(self, structure: Dict[str, Any]) -> bool:
        """
        验证任务结构的正确性
        
        Args:
            structure: 任务结构信息
            
        Returns:
            是否验证通过
            
        开发者: @ConceptualGod
        """
        # 检查是否有基本的结构元素
        required_fields = ['possible_task_id', 'possible_progress', 'possible_target']
        
        for field in required_fields:
            if not structure.get(field):
                return False
        
        # 检查进度值的合理性
        progress = structure['possible_progress']['value']
        target = structure['possible_target']['value']
        
        if progress > target or target <= 0:
            return False
        
        return True
    
    def generate_memory_map(self) -> Dict[str, Any]:
        """
        生成内存映射
        
        Returns:
            内存映射信息
            
        开发者: @ConceptualGod
        """
        memory_map = {
            'scan_time': time.strftime('%Y-%m-%d %H:%M:%S'),
            'process_info': {
                'name': self.analyzer.game_process.name() if self.analyzer.game_process else None,
                'pid': self.analyzer.game_process.pid if self.analyzer.game_process else None
            },
            'task_candidates': self.task_candidates,
            'verified_structures': self.verified_structures,
            'recommended_offsets': self.calculate_offsets()
        }
        
        return memory_map
    
    def calculate_offsets(self) -> Dict[str, Any]:
        """
        计算推荐的内存偏移
        
        Returns:
            偏移信息
            
        开发者: @ConceptualGod
        """
        if not self.task_candidates:
            return {}
        
        # 分析任务结构的通用模式
        offsets = {
            'task_id_offset': None,
            'progress_offset': None,
            'target_offset': None,
            'status_offset': None,
            'desc_offset': None
        }
        
        # 基于第一个验证的结构计算偏移
        if self.task_candidates:
            first_task = self.task_candidates[0]
            
            if first_task.get('possible_task_id'):
                offsets['task_id_offset'] = first_task['possible_task_id']['offset']
            
            if first_task.get('possible_progress'):
                offsets['progress_offset'] = first_task['possible_progress']['offset']
            
            if first_task.get('possible_target'):
                offsets['target_offset'] = first_task['possible_target']['offset']
            
            if first_task.get('possible_status'):
                offsets['status_offset'] = first_task['possible_status']['offset']
        
        return offsets
    
    def save_results(self, filename: str = 'zhangong_memory_map.json'):
        """
        保存扫描结果
        
        Args:
            filename: 保存文件名
            
        开发者: @ConceptualGod
        """
        memory_map = self.generate_memory_map()
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(memory_map, f, ensure_ascii=False, indent=2)
        
        print(f"扫描结果已保存到 {filename} - By @ConceptualGod")
    
    def run_full_scan(self):
        """
        执行完整扫描
        
        开发者: @ConceptualGod
        """
        print("开始完整战功内存扫描 - By @ConceptualGod")
        print("=" * 50)
        
        try:
            # 1. 扫描任务数据
            if not self.scan_for_task_data():
                print("未找到任务数据")
                return
            
            # 2. 验证找到的结构
            for structure in self.task_candidates:
                if self.verify_task_structure(structure):
                    self.verified_structures.append(structure)
            
            print(f"验证通过的结构: {len(self.verified_structures)} 个")
            
            # 3. 扫描任务列表
            list_structures = self.scan_for_task_list()
            print(f"找到任务列表结构: {len(list_structures)} 个")
            
            # 4. 保存结果
            self.save_results()
            
            # 5. 输出摘要
            self.print_summary()
            
        except Exception as e:
            print(f"扫描过程中出现异常: {e} - By @ConceptualGod")
        
        finally:
            self.analyzer.close_handle()
    
    def print_summary(self):
        """
        打印扫描摘要
        
        开发者: @ConceptualGod
        """
        print("\n" + "=" * 50)
        print("扫描摘要 - By @ConceptualGod")
        print("=" * 50)
        
        print(f"任务候选结构: {len(self.task_candidates)} 个")
        print(f"验证通过结构: {len(self.verified_structures)} 个")
        
        if self.verified_structures:
            print("\n推荐的内存结构:")
            offsets = self.calculate_offsets()
            for key, value in offsets.items():
                if value is not None:
                    print(f"  {key}: {value}")
        
        print("\n扫描完成 - By @ConceptualGod")


def main():
    """
    主函数
    
    开发者: @ConceptualGod
    """
    print("起凡战功内存扫描器 By @ConceptualGod")
    print("请确保游戏正在运行且已登录到主界面")
    input("按回车键开始扫描...")
    
    scanner = ZhanGongMemoryScanner()
    scanner.run_full_scan()


if __name__ == "__main__":
    main()
