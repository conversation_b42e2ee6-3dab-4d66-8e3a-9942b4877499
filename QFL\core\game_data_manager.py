#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
起凡游戏数据管理器

根据自动化说明，只管理需要的数据：
- 6个指定英雄：华佗、刘备、诸葛瑾、陆逊、孙权、曹操
- 5种任务类型：助攻任务、完整局任务、胜利局任务、MVP任务、牺牲值任务
- 3个国家分类：蜀国、魏国、中立

开发者: @ConceptualGod
"""

import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from .game_data_reader import <PERSON><PERSON><PERSON>ongTaskReader, HeroDataReader, ConfigReader


class GameDataManager:
    """游戏数据管理器"""
    
    def __init__(self, game_path: str):
        """
        初始化数据管理器
        
        Args:
            game_path: 7fgame文件夹路径
        """
        self.game_path = Path(game_path)
        self.logger = logging.getLogger(__name__)
        
        # 初始化各个数据读取器
        self.zhangong_reader = ZhanGongTaskReader(game_path)
        self.hero_reader = HeroDataReader(game_path)
        self.config_reader = ConfigReader(game_path)
        
        # 自动化说明中明确的需求数据
        self.target_heroes = ['华佗', '刘备', '诸葛瑾', '陆逊', '孙权', '曹操']
        self.target_task_types = ['助攻任务', '完整局任务', '胜利局任务', 'MVP任务', '牺牲值任务']
        self.hero_countries = {
            '华佗': '蜀国',
            '刘备': '蜀国', 
            '曹操': '魏国',
            '诸葛瑾': '中立',
            '陆逊': '中立',
            '孙权': '中立'
        }
    
    def get_available_tasks(self) -> List[Dict[str, Any]]:
        """
        获取可用的战功任务（仅返回5种需要的任务类型）
        
        Returns:
            可用任务列表
        """
        all_tasks = self.zhangong_reader.read_zhangong_tasks()
        available_tasks = []
        
        for task in all_tasks:
            task_type = self.zhangong_reader.identify_task_type(task)
            if task_type in self.target_task_types:
                task['task_type'] = task_type
                available_tasks.append(task)
        
        self.logger.info(f"找到 {len(available_tasks)} 个可用的战功任务")
        return available_tasks
    
    def get_suitable_hero_for_task(self, task: Dict[str, Any]) -> Optional[str]:
        """
        根据任务获取合适的英雄（从6个指定英雄中选择）
        
        Args:
            task: 任务信息
            
        Returns:
            推荐的英雄名称
        """
        task_type = task.get('task_type', '')
        
        # 根据任务类型推荐英雄
        if task_type == '助攻任务':
            # 助攻任务推荐辅助型英雄
            return '华佗'  # 华佗有治疗技能，容易获得助攻
        elif task_type == '完整局任务':
            # 完整局任务任意英雄都可以
            return self.target_heroes[0]  # 默认选择第一个
        elif task_type == '胜利局任务':
            # 胜利局任务推荐强力英雄
            return '曹操'  # 曹操攻击力强
        elif task_type == 'MVP任务':
            # MVP任务推荐输出型英雄
            return '曹操'
        elif task_type == '牺牲值任务':
            # 牺牲值任务推荐坦克型英雄
            return '刘备'  # 刘备比较肉
        else:
            # 默认选择华佗
            return '华佗'
    
    def get_hero_by_country(self, country: str) -> List[str]:
        """
        根据国家获取英雄列表
        
        Args:
            country: 国家名称 ('蜀国', '魏国', '中立')
            
        Returns:
            该国家的英雄名称列表
        """
        heroes = []
        for hero_name, hero_country in self.hero_countries.items():
            if hero_country == country:
                heroes.append(hero_name)
        
        return heroes
    
    def check_task_hero_match(self, task: Dict[str, Any], hero_name: str) -> bool:
        """
        检查任务和英雄是否匹配
        
        Args:
            task: 任务信息
            hero_name: 英雄名称
            
        Returns:
            是否匹配
        """
        # 检查英雄是否在目标英雄列表中
        if hero_name not in self.target_heroes:
            return False
        
        # 检查任务类型是否在目标任务类型中
        task_type = self.zhangong_reader.identify_task_type(task)
        if task_type not in self.target_task_types:
            return False
        
        return True
    
    def get_current_account_info(self) -> Dict[str, Any]:
        """
        获取当前账号信息
        
        Returns:
            账号信息字典
        """
        return self.config_reader.read_current_account()
    
    def get_task_progress_requirements(self, task: Dict[str, Any]) -> Dict[str, Any]:
        """
        获取任务进度要求
        
        Args:
            task: 任务信息
            
        Returns:
            任务要求字典
        """
        task_type = self.zhangong_reader.identify_task_type(task)
        
        requirements = {
            'task_type': task_type,
            'target_value': task.get('Value', 0),
            'description': task.get('Desc', ''),
            'task_id': task.get('Id', 0)
        }
        
        # 根据任务类型添加具体要求
        if task_type == '助攻任务':
            requirements['metric'] = 'assists'
            requirements['unit'] = '个'
        elif task_type == '完整局任务':
            requirements['metric'] = 'complete_games'
            requirements['unit'] = '局'
        elif task_type == '胜利局任务':
            requirements['metric'] = 'victories'
            requirements['unit'] = '局'
        elif task_type == 'MVP任务':
            requirements['metric'] = 'mvp_score'
            requirements['unit'] = '分'
        elif task_type == '牺牲值任务':
            requirements['metric'] = 'sacrifice_value'
            requirements['unit'] = '点'
        
        return requirements
    
    def get_hero_skills_for_task(self, hero_name: str) -> List[str]:
        """
        根据自动化说明获取英雄技能
        
        Args:
            hero_name: 英雄名称
            
        Returns:
            技能列表
        """
        # 根据自动化说明中的技能配置
        hero_skills = {
            '华佗': ['W', 'D'],  # 华佗：W和D
            '刘备': ['C', 'E'],  # 刘备：C和E
            '诸葛瑾': ['E', 'W'],  # 诸葛瑾：E和W
            '陆逊': ['E'],  # 陆逊：E
            '孙权': ['E'],  # 孙权：E
            '曹操': ['C']   # 曹操：C
        }
        
        return hero_skills.get(hero_name, [])
    
    def is_cure_hero(self, hero_name: str) -> bool:
        """
        判断是否为治疗英雄
        
        Args:
            hero_name: 英雄名称
            
        Returns:
            是否为治疗英雄
        """
        # 根据自动化说明，华佗有治疗技能W
        return hero_name == '华佗'
    
    def get_task_summary(self) -> Dict[str, Any]:
        """
        获取任务总览
        
        Returns:
            任务总览信息
        """
        available_tasks = self.get_available_tasks()
        
        summary = {
            'total_tasks': len(available_tasks),
            'task_types': {},
            'recommended_heroes': {}
        }
        
        # 统计各类型任务数量
        for task in available_tasks:
            task_type = task.get('task_type', '其他任务')
            summary['task_types'][task_type] = summary['task_types'].get(task_type, 0) + 1
            
            # 为每种任务类型推荐英雄
            if task_type not in summary['recommended_heroes']:
                recommended_hero = self.get_suitable_hero_for_task(task)
                summary['recommended_heroes'][task_type] = recommended_hero
        
        return summary
    
    def validate_game_data(self) -> Dict[str, bool]:
        """
        验证游戏数据完整性
        
        Returns:
            验证结果
        """
        validation = {
            'zhangong_tasks_available': False,
            'hero_data_available': False,
            'config_readable': False,
            'target_heroes_found': False
        }
        
        try:
            # 检查战功任务数据
            tasks = self.zhangong_reader.read_zhangong_tasks()
            validation['zhangong_tasks_available'] = len(tasks) > 0
            
            # 检查英雄数据
            heroes = self.hero_reader.read_hero_list()
            validation['hero_data_available'] = len(heroes) > 0
            
            # 检查配置文件
            config = self.config_reader.read_game_config()
            validation['config_readable'] = len(config) > 0
            
            # 检查目标英雄是否都能找到
            hero_mapping = self.hero_reader.build_hero_mapping()
            found_heroes = [hero for hero in self.target_heroes if hero in hero_mapping]
            validation['target_heroes_found'] = len(found_heroes) == len(self.target_heroes)
            
        except Exception as e:
            self.logger.error(f"验证游戏数据时出错: {e}")
        
        return validation
