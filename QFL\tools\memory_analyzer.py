#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
起凡游戏内存分析工具

用于逆向分析游戏内存结构，获取战功任务的真实内存布局
开发者: @ConceptualGod
创建时间: 2025-07-28
"""

import ctypes
import ctypes.wintypes
import psutil
import struct
import time
import re
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass


@dataclass
class MemoryRegion:
    """
    内存区域信息
    
    开发者: @ConceptualGod
    """
    base_address: int
    size: int
    protection: int
    type: int


class GameMemoryAnalyzer:
    """
    游戏内存分析器
    
    开发者: @ConceptualGod
    """
    
    def __init__(self):
        """
        初始化内存分析器
        
        开发者: @ConceptualGod
        """
        self.kernel32 = ctypes.windll.kernel32
        self.process_handle = None
        self.game_process = None
        self.base_address = None
        
        # 内存访问权限
        self.PROCESS_ALL_ACCESS = 0x1F0FFF
        self.PROCESS_VM_READ = 0x0010
        self.PROCESS_QUERY_INFORMATION = 0x0400
        
        # 内存保护类型
        self.PAGE_EXECUTE_READWRITE = 0x40
        self.PAGE_READWRITE = 0x04
        self.PAGE_READONLY = 0x02
        
        # 内存类型
        self.MEM_COMMIT = 0x1000
        self.MEM_PRIVATE = 0x20000
        
        print("内存分析器初始化完成 - By @ConceptualGod")
    
    def find_game_process(self) -> bool:
        """
        查找游戏进程
        
        Returns:
            是否找到游戏进程
            
        开发者: @ConceptualGod
        """
        target_processes = ['7FGame.exe', 'game.exe', '7fsanguo.exe']
        
        for proc in psutil.process_iter(['pid', 'name', 'memory_info']):
            if proc.info['name'] in target_processes:
                self.game_process = proc
                print(f"找到游戏进程: {proc.info['name']} (PID: {proc.info['pid']}) - By @ConceptualGod")
                print(f"内存使用: {proc.info['memory_info'].rss / 1024 / 1024:.1f} MB")
                return True
        
        print("未找到游戏进程 - By @ConceptualGod")
        return False
    
    def open_process_handle(self) -> bool:
        """
        打开进程句柄
        
        Returns:
            是否成功打开
            
        开发者: @ConceptualGod
        """
        if not self.game_process:
            print("游戏进程未找到 - By @ConceptualGod")
            return False
        
        pid = self.game_process.pid
        self.process_handle = self.kernel32.OpenProcess(
            self.PROCESS_VM_READ | self.PROCESS_QUERY_INFORMATION,
            False,
            pid
        )
        
        if self.process_handle:
            print(f"成功打开进程句柄 - By @ConceptualGod")
            return True
        else:
            error_code = self.kernel32.GetLastError()
            print(f"打开进程句柄失败，错误代码: {error_code} - By @ConceptualGod")
            return False
    
    def get_memory_regions(self) -> List[MemoryRegion]:
        """
        获取进程内存区域
        
        Returns:
            内存区域列表
            
        开发者: @ConceptualGod
        """
        regions = []
        
        if not self.process_handle:
            return regions
        
        # MEMORY_BASIC_INFORMATION 结构
        class MEMORY_BASIC_INFORMATION(ctypes.Structure):
            _fields_ = [
                ("BaseAddress", ctypes.c_void_p),
                ("AllocationBase", ctypes.c_void_p),
                ("AllocationProtect", ctypes.wintypes.DWORD),
                ("RegionSize", ctypes.c_size_t),
                ("State", ctypes.wintypes.DWORD),
                ("Protect", ctypes.wintypes.DWORD),
                ("Type", ctypes.wintypes.DWORD),
            ]
        
        mbi = MEMORY_BASIC_INFORMATION()
        address = 0
        
        while address < 0x7FFFFFFF:  # 用户空间地址范围
            result = self.kernel32.VirtualQueryEx(
                self.process_handle,
                ctypes.c_void_p(address),
                ctypes.byref(mbi),
                ctypes.sizeof(mbi)
            )
            
            if result == 0:
                break
            
            # 只关注已提交的可读内存
            if (mbi.State == self.MEM_COMMIT and 
                mbi.Protect & (self.PAGE_READWRITE | self.PAGE_READONLY | self.PAGE_EXECUTE_READWRITE)):
                
                regions.append(MemoryRegion(
                    base_address=mbi.BaseAddress,
                    size=mbi.RegionSize,
                    protection=mbi.Protect,
                    type=mbi.Type
                ))
            
            address = mbi.BaseAddress + mbi.RegionSize
        
        print(f"找到 {len(regions)} 个可读内存区域 - By @ConceptualGod")
        return regions
    
    def read_memory_safe(self, address: int, size: int) -> Optional[bytes]:
        """
        安全读取内存
        
        Args:
            address: 内存地址
            size: 读取大小
            
        Returns:
            读取的数据或None
            
        开发者: @ConceptualGod
        """
        try:
            buffer = ctypes.create_string_buffer(size)
            bytes_read = ctypes.wintypes.SIZE_T()
            
            result = self.kernel32.ReadProcessMemory(
                self.process_handle,
                ctypes.c_void_p(address),
                buffer,
                size,
                ctypes.byref(bytes_read)
            )
            
            if result and bytes_read.value > 0:
                return buffer.raw[:bytes_read.value]
            else:
                return None
                
        except Exception:
            return None
    
    def search_pattern(self, pattern: bytes, regions: List[MemoryRegion] = None) -> List[int]:
        """
        在内存中搜索字节模式
        
        Args:
            pattern: 要搜索的字节模式
            regions: 搜索的内存区域列表
            
        Returns:
            找到的地址列表
            
        开发者: @ConceptualGod
        """
        found_addresses = []
        
        if regions is None:
            regions = self.get_memory_regions()
        
        print(f"开始搜索模式: {pattern.hex()} - By @ConceptualGod")
        
        for region in regions:
            # 检查region.base_address是否有效
            if region.base_address is None or region.base_address == 0:
                continue

            # 分块读取内存（每次1MB）
            chunk_size = 1024 * 1024
            overlap = len(pattern) - 1

            for offset in range(0, region.size, chunk_size - overlap):
                read_size = min(chunk_size, region.size - offset)
                if read_size < len(pattern):
                    break

                data = self.read_memory_safe(region.base_address + offset, read_size)
                if not data:
                    continue

                # 在数据中搜索模式
                pos = 0
                while True:
                    pos = data.find(pattern, pos)
                    if pos == -1:
                        break

                    found_address = region.base_address + offset + pos
                    found_addresses.append(found_address)
                    pos += 1
        
        print(f"找到 {len(found_addresses)} 个匹配地址 - By @ConceptualGod")
        return found_addresses
    
    def search_string(self, text: str, encoding: str = 'utf-8') -> List[int]:
        """
        搜索字符串
        
        Args:
            text: 要搜索的文本
            encoding: 字符编码
            
        Returns:
            找到的地址列表
            
        开发者: @ConceptualGod
        """
        try:
            pattern = text.encode(encoding)
            return self.search_pattern(pattern)
        except UnicodeEncodeError:
            print(f"字符串编码失败: {text} - By @ConceptualGod")
            return []
    
    def search_zhangong_keywords(self) -> Dict[str, List[int]]:
        """
        搜索战功相关关键字
        
        Returns:
            关键字地址字典
            
        开发者: @ConceptualGod
        """
        keywords = {
            "战功": ["战功", "zhangong"],
            "任务": ["任务", "task"],
            "助攻": ["助攻", "assist"],
            "胜利": ["胜利", "victory", "win"],
            "完整": ["完整", "complete"],
            "MVP": ["MVP", "mvp"],
            "牺牲值": ["牺牲值", "sacrifice"]
        }
        
        results = {}
        
        for category, terms in keywords.items():
            results[category] = []
            
            for term in terms:
                # 尝试不同编码
                for encoding in ['utf-8', 'gbk', 'utf-16le']:
                    try:
                        addresses = self.search_string(term, encoding)
                        results[category].extend(addresses)
                        if addresses:
                            print(f"找到 '{term}' ({encoding}): {len(addresses)} 个位置 - By @ConceptualGod")
                    except:
                        continue
        
        return results
    
    def analyze_memory_around_address(self, address: int, size: int = 1024) -> Dict[str, Any]:
        """
        分析指定地址周围的内存
        
        Args:
            address: 目标地址
            size: 分析大小
            
        Returns:
            分析结果
            
        开发者: @ConceptualGod
        """
        # 确保起始地址不为负数
        start_address = max(0, address - size//2)
        data = self.read_memory_safe(start_address, size)
        if not data:
            return {"error": "无法读取内存"}
        
        analysis = {
            "address": f"0x{address:X}",
            "size": len(data),
            "hex_dump": data.hex(),
            "ascii_strings": [],
            "unicode_strings": [],
            "possible_integers": [],
            "possible_pointers": []
        }
        
        # 查找ASCII字符串
        ascii_pattern = re.compile(b'[\x20-\x7E]{4,}')
        for match in ascii_pattern.finditer(data):
            try:
                string = match.group().decode('ascii')
                analysis["ascii_strings"].append({
                    "offset": match.start(),
                    "string": string
                })
            except:
                pass
        
        # 查找Unicode字符串
        unicode_pattern = re.compile(b'(?:[\x20-\x7E]\x00){4,}')
        for match in unicode_pattern.finditer(data):
            try:
                string = match.group().decode('utf-16le')
                analysis["unicode_strings"].append({
                    "offset": match.start(),
                    "string": string
                })
            except:
                pass
        
        # 分析可能的整数值
        for i in range(0, len(data) - 4, 4):
            try:
                value = struct.unpack('<I', data[i:i+4])[0]
                if 0 < value < 1000000:  # 可能的计数器或ID
                    analysis["possible_integers"].append({
                        "offset": i,
                        "value": value
                    })
            except:
                pass
        
        # 分析可能的指针
        for i in range(0, len(data) - 8, 4):
            try:
                value = struct.unpack('<Q', data[i:i+8])[0] if len(data) >= i+8 else struct.unpack('<I', data[i:i+4])[0]
                if 0x400000 <= value <= 0x7FFFFFFF:  # 可能的用户空间指针
                    analysis["possible_pointers"].append({
                        "offset": i,
                        "value": f"0x{value:X}"
                    })
            except:
                pass
        
        return analysis
    
    def find_task_structures(self) -> List[Dict[str, Any]]:
        """
        查找任务结构体
        
        Returns:
            可能的任务结构列表
            
        开发者: @ConceptualGod
        """
        print("开始查找任务结构体 - By @ConceptualGod")
        
        # 搜索战功相关关键字
        keyword_results = self.search_zhangong_keywords()
        
        potential_structures = []
        
        # 分析每个关键字周围的内存
        for category, addresses in keyword_results.items():
            print(f"分析 '{category}' 相关内存区域...")
            
            for addr in addresses[:5]:  # 只分析前5个地址
                analysis = self.analyze_memory_around_address(addr, 2048)
                
                if analysis.get("possible_integers") or analysis.get("possible_pointers"):
                    potential_structures.append({
                        "category": category,
                        "address": addr,
                        "analysis": analysis
                    })
        
        print(f"找到 {len(potential_structures)} 个潜在结构 - By @ConceptualGod")
        return potential_structures
    
    def dump_memory_region(self, address: int, size: int, filename: str):
        """
        导出内存区域到文件
        
        Args:
            address: 起始地址
            size: 大小
            filename: 文件名
            
        开发者: @ConceptualGod
        """
        data = self.read_memory_safe(address, size)
        if data:
            with open(filename, 'wb') as f:
                f.write(data)
            print(f"内存区域已导出到 {filename} - By @ConceptualGod")
        else:
            print(f"无法读取内存区域 0x{address:X} - By @ConceptualGod")
    
    def close_handle(self):
        """
        关闭进程句柄
        
        开发者: @ConceptualGod
        """
        if self.process_handle:
            self.kernel32.CloseHandle(self.process_handle)
            self.process_handle = None
            print("进程句柄已关闭 - By @ConceptualGod")


def main():
    """
    主函数 - 执行内存分析
    
    开发者: @ConceptualGod
    """
    print("起凡游戏内存分析工具 By @ConceptualGod")
    print("=" * 50)
    
    analyzer = GameMemoryAnalyzer()
    
    try:
        # 1. 查找游戏进程
        if not analyzer.find_game_process():
            print("请先启动起凡游戏")
            return
        
        # 2. 打开进程句柄
        if not analyzer.open_process_handle():
            print("无法打开进程句柄，请以管理员身份运行")
            return
        
        # 3. 获取内存区域
        regions = analyzer.get_memory_regions()
        print(f"可分析内存区域: {len(regions)} 个")
        
        # 4. 查找任务结构
        structures = analyzer.find_task_structures()
        
        # 5. 输出分析结果
        print("\n" + "=" * 50)
        print("分析结果 - By @ConceptualGod")
        print("=" * 50)
        
        for i, struct in enumerate(structures):
            print(f"\n结构 {i+1}:")
            print(f"  类别: {struct['category']}")
            print(f"  地址: 0x{struct['address']:X}")
            print(f"  整数值: {len(struct['analysis'].get('possible_integers', []))} 个")
            print(f"  指针值: {len(struct['analysis'].get('possible_pointers', []))} 个")
            print(f"  字符串: {len(struct['analysis'].get('ascii_strings', []))} 个")
        
        # 6. 保存详细分析结果
        import json
        with open('memory_analysis_result.json', 'w', encoding='utf-8') as f:
            json.dump(structures, f, ensure_ascii=False, indent=2)
        
        print(f"\n详细分析结果已保存到 memory_analysis_result.json - By @ConceptualGod")
        
    except Exception as e:
        print(f"分析过程中出现异常: {e} - By @ConceptualGod")
    
    finally:
        analyzer.close_handle()
        print("分析完成 - By @ConceptualGod")


if __name__ == "__main__":
    main()
