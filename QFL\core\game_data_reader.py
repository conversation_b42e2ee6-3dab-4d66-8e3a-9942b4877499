#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
起凡游戏数据读取模块

根据7fgame文件夹重新扫描分析，实现游戏数据的只读访问
开发者: @ConceptualGod
"""

import json
import xml.etree.ElementTree as ET
import configparser
import logging
from pathlib import Path
from typing import Dict, List, Any, Optional
from abc import ABC, abstractmethod


class GameDataReader(ABC):
    """游戏数据读取器基类"""
    
    def __init__(self, game_path: str):
        """
        初始化数据读取器
        
        Args:
            game_path: 7fgame文件夹路径
        """
        self.game_path = Path(game_path)
        self.logger = logging.getLogger(self.__class__.__name__)
        
        if not self.game_path.exists():
            raise FileNotFoundError(f"游戏路径不存在: {game_path}")
    
    @abstractmethod
    def read_data(self) -> Any:
        """读取数据的抽象方法"""
        pass
    
    def _safe_read_file(self, file_path: Path, encoding: str = 'utf-8') -> Optional[str]:
        """
        安全读取文件内容
        
        Args:
            file_path: 文件路径
            encoding: 文件编码
            
        Returns:
            文件内容或None
        """
        try:
            if not file_path.exists():
                self.logger.warning(f"文件不存在: {file_path}")
                return None
            
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
                
        except UnicodeDecodeError:
            # 尝试其他编码
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    return f.read()
            except Exception as e:
                self.logger.error(f"读取文件失败 {file_path}: {e}")
                return None
        except Exception as e:
            self.logger.error(f"读取文件异常 {file_path}: {e}")
            return None


class ZhanGongTaskReader(GameDataReader):
    """战功任务数据读取器"""
    
    def read_data(self) -> Dict[str, Any]:
        """读取战功任务数据"""
        return {
            'tasks': self.read_zhangong_tasks(),
            'cure_heroes': self.read_cure_heroes(),
            'task_config': self.read_task_config()
        }
    
    def read_zhangong_tasks(self) -> List[Dict[str, Any]]:
        """
        读取战功任务列表
        
        Returns:
            战功任务列表
        """
        task_file = self.game_path / "Data" / "ZhanGongTask.json"
        content = self._safe_read_file(task_file)
        
        if not content:
            return []
        
        try:
            tasks = json.loads(content)
            self.logger.info(f"成功读取 {len(tasks)} 个战功任务")
            return tasks
        except json.JSONDecodeError as e:
            self.logger.error(f"解析战功任务JSON失败: {e}")
            return []
    
    def read_cure_heroes(self) -> List[str]:
        """
        读取治疗英雄列表
        
        Returns:
            治疗英雄ID列表
        """
        config_file = self.game_path / "Data" / "zhangong.json"
        content = self._safe_read_file(config_file)
        
        if not content:
            return []
        
        try:
            config = json.loads(content)
            cure_heroes_str = config.get('cure_heros', '')
            cure_heroes = [hero_id.strip() for hero_id in cure_heroes_str.split(',') if hero_id.strip()]
            self.logger.info(f"成功读取 {len(cure_heroes)} 个治疗英雄ID")
            return cure_heroes
        except json.JSONDecodeError as e:
            self.logger.error(f"解析治疗英雄配置JSON失败: {e}")
            return []
    
    def read_task_config(self) -> Dict[str, Any]:
        """
        读取任务配置
        
        Returns:
            任务配置字典
        """
        config_file = self.game_path / "Data" / "zhangong.json"
        content = self._safe_read_file(config_file)
        
        if not content:
            return {}
        
        try:
            config = json.loads(content)
            self.logger.info("成功读取任务配置")
            return config
        except json.JSONDecodeError as e:
            self.logger.error(f"解析任务配置JSON失败: {e}")
            return {}
    
    def identify_task_type(self, task: Dict[str, Any]) -> str:
        """
        识别任务类型（仅识别自动化说明中的5种任务类型）

        Args:
            task: 任务信息

        Returns:
            任务类型字符串
        """
        desc = task.get('Desc', '')

        # 只识别自动化说明中明确提到的5种任务类型
        if '助攻' in desc:
            return '助攻任务'
        elif '完整' in desc:
            return '完整局任务'
        elif '胜利' in desc:
            return '胜利局任务'
        elif 'mvp' in desc.lower() or 'MVP' in desc:
            return 'MVP任务'
        elif '牺牲值' in desc:
            return '牺牲值任务'
        else:
            # 不在需求范围内的任务
            return '其他任务'


class HeroDataReader(GameDataReader):
    """英雄数据读取器"""
    
    def read_data(self) -> Dict[str, Any]:
        """读取英雄数据"""
        return {
            'heroes': self.read_hero_list(),
            'hero_mapping': self.build_hero_mapping()
        }
    
    def read_hero_list(self) -> List[Dict[str, Any]]:
        """
        读取英雄列表
        
        Returns:
            英雄列表
        """
        hero_file = self.game_path / "Data" / "sanguo" / "hero.xml"
        content = self._safe_read_file(hero_file)
        
        if not content:
            return []
        
        try:
            root = ET.fromstring(content)
            heroes = []
            
            for hero_elem in root.findall('Hero'):
                filename_elem = hero_elem.find('FileName')
                name_elem = hero_elem.find('HeroName')
                
                if filename_elem is not None and name_elem is not None:
                    hero = {
                        'filename': filename_elem.text,
                        'name': name_elem.text,
                        'country': self._determine_hero_country(name_elem.text)
                    }
                    heroes.append(hero)
            
            self.logger.info(f"成功读取 {len(heroes)} 个英雄信息")
            return heroes
            
        except ET.ParseError as e:
            self.logger.error(f"解析英雄XML失败: {e}")
            return []
    
    def build_hero_mapping(self) -> Dict[str, Dict[str, Any]]:
        """
        构建英雄名称到信息的映射
        
        Returns:
            英雄映射字典
        """
        heroes = self.read_hero_list()
        mapping = {}
        
        for hero in heroes:
            mapping[hero['name']] = hero
        
        return mapping
    
    def get_hero_by_name(self, name: str) -> Optional[Dict[str, Any]]:
        """
        根据名称获取英雄信息
        
        Args:
            name: 英雄名称
            
        Returns:
            英雄信息字典
        """
        mapping = self.build_hero_mapping()
        return mapping.get(name)
    
    def get_heroes_by_country(self, country: str) -> List[Dict[str, Any]]:
        """
        根据国家获取英雄列表（仅返回自动化说明中的6个英雄）

        Args:
            country: 国家名称 ('蜀国', '魏国', '中立')

        Returns:
            该国家的英雄列表
        """
        # 只返回自动化说明中明确提到的6个英雄
        target_heroes = ['华佗', '刘备', '诸葛瑾', '陆逊', '孙权', '曹操']
        heroes = self.read_hero_list()
        return [hero for hero in heroes
                if hero.get('country') == country and hero.get('name') in target_heroes]
    
    def _determine_hero_country(self, hero_name: str) -> str:
        """
        根据英雄名称判断国家归属（仅识别自动化说明中提到的6个英雄）

        Args:
            hero_name: 英雄名称

        Returns:
            国家名称
        """
        # 根据起凡游戏自动化说明.md中的明确分类
        if hero_name in ['华佗', '刘备']:
            return '蜀国'
        elif hero_name == '曹操':
            return '魏国'
        elif hero_name in ['诸葛瑾', '陆逊', '孙权']:
            return '中立'
        else:
            # 其他英雄不在自动化需求范围内
            return '其他'


class ConfigReader(GameDataReader):
    """配置文件读取器"""
    
    def read_data(self) -> Dict[str, Any]:
        """读取配置数据"""
        return {
            'game_config': self.read_game_config(),
            'current_account': self.read_current_account(),
            'window_settings': self.read_window_settings()
        }
    
    def read_game_config(self) -> Dict[str, Any]:
        """
        读取游戏主配置
        
        Returns:
            游戏配置字典
        """
        config_file = self.game_path / "7FGame.ini"
        content = self._safe_read_file(config_file)
        
        if not content:
            return {}
        
        try:
            # 处理INI文件的编码问题
            config = configparser.ConfigParser()
            config.read_string(content)
            
            result = {}
            for section in config.sections():
                result[section] = dict(config[section])
            
            self.logger.info("成功读取游戏配置")
            return result
            
        except Exception as e:
            self.logger.error(f"解析游戏配置失败: {e}")
            return {}
    
    def read_current_account(self) -> Dict[str, Any]:
        """
        读取当前登录账号信息
        
        Returns:
            账号信息字典
        """
        config = self.read_game_config()
        log_section = config.get('LOG', {})
        
        return {
            'login_name': log_section.get('LoginName', ''),
            'login_id': log_section.get('LoginID', ''),
            'game_path': log_section.get('GamePath', '')
        }
    
    def read_window_settings(self) -> Dict[str, Any]:
        """
        读取窗口设置
        
        Returns:
            窗口设置字典
        """
        config = self.read_game_config()
        setting_section = config.get('Setting', {})
        main_wnd_section = config.get('MainWnd', {})
        
        return {
            'width': setting_section.get('MainWndW', '1936'),
            'height': setting_section.get('MainWndH', '1096'),
            'zoomed': main_wnd_section.get('Zoomed', '1'),
            'left_pane_width': main_wnd_section.get('LeftPaneWidth', '95')
        }
